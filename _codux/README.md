# Codux Design System Integration

This directory contains all the necessary files to use the design system within Codux boards and components.

## Files Overview

### Core Setup Files
- `board-global-setup.ts` - Global setup that imports the design system for all boards
- `design-system-setup.ts` - Comprehensive design system imports and utilities
- `component-templates/design-system-component.board.tsx` - Example board showing design system usage

### Board Files
- `boards/` - Individual component boards
- `board-wrappers/` - Wrapper components for boards
- `wrappers/` - Page and component wrappers
- `ui-kit/` - UI kit components and documentation

## Using Design System in Codux

### 1. Automatic Setup
All boards automatically have access to the design system through `board-global-setup.ts`. This includes:
- All SCSS variables and mixins
- Global styles and utility classes
- Typography and color systems

### 2. Using Design Tokens in JavaScript/TypeScript
```typescript
import { designTokens, getColor, getSpacing } from '_codux/design-system-setup';

// Use design tokens directly
const myStyle = {
  padding: designTokens.spacing.xl,
  backgroundColor: designTokens.colors.primary.medium,
  borderRadius: designTokens.borderRadius.lg,
};

// Use utility functions
const myOtherStyle = {
  padding: getSpacing('xl'),
  color: getColor('accent', 'green'),
};
```

### 3. Using CSS Classes
All utility classes from the design system are available:
```tsx
<div className="card-base glass-card">
  <h2 className="text-gradient text-2xl font-heading">Title</h2>
  <p className="text-primary text-base">Content</p>
  <button className="button-primary button-small">Action</button>
</div>
```

### 4. Using SCSS Mixins in Board Styles
```scss
@import '../styles/mixins.scss';

.my-component {
  @include card-base;
  
  .title {
    @include heading-base;
    @include responsive-text($font-size-lg, $font-size-2xl);
  }
  
  .button {
    @include button-primary;
    @include button-small;
  }
}
```

## Available Design Tokens

### Colors
- **Primary**: `primary.dark`, `primary.medium`, `primary.light`
- **Accent**: `accent.green`, `accent.greenLight`, `accent.purple`, `accent.purpleLight`
- **Neutral**: `neutral.white`, `neutral.black`, `neutral.gray100-900`
- **Text**: `text.primary`, `text.secondary`, `text.light`, `text.muted`
- **Status**: `status.success`, `status.warning`, `status.error`, `status.info`

### Spacing
- `xs` (4px) to `7xl` (80px)
- Use: `designTokens.spacing.xl` or `getSpacing('xl')`

### Typography
- **Font Families**: `primary` (DM Sans), `heading` (Audiowide), `mono` (Monaco)
- **Font Sizes**: `xs` (12px) to `6xl` (60px)
- **Font Weights**: `light` (300) to `black` (900)

### Shadows
- `sm`, `md`, `lg`, `xl`, `2xl`, `card`, `cardHover`

### Border Radius
- `sm` (8px) to `full` (50px)

## Component Mixins Available

### Cards
- `@include card-base` - Standard card styling
- `@include glass-card` - Glassmorphism effect
- `@include clickable-card` - Interactive card

### Buttons
- `@include button-primary` - Main action buttons
- `@include button-secondary` - Secondary actions
- `@include button-ghost` - Outline buttons
- `@include button-small` / `@include button-large` - Size variants

### Forms
- `@include input-base` - Standard input styling

### Typography
- `@include heading-base` - Consistent heading styles
- `@include text-gradient` - Gradient text effect
- `@include responsive-text($mobile, $desktop)` - Responsive font sizes

### Layout
- `@include section-container` - Page section wrapper
- `@include flex-center` - Center content with flexbox

### Interactive Elements
- `@include hover-lift($lift, $scale)` - Hover lift effect
- `@include interactive-element` - Basic interactive styling
- `@include focus-ring` - Accessibility focus ring

## Best Practices for Codux Boards

1. **Use ComponentWrapper**: Wrap components in `ComponentWrapper` for proper context
2. **Import design system**: Use `import { designTokens } from '_codux/design-system-setup'`
3. **Use semantic tokens**: Prefer `designTokens.colors.primary.medium` over hex codes
4. **Test responsive behavior**: Use different window sizes in board environment props
5. **Document variants**: Use Codux variants to show different states
6. **Follow naming conventions**: Use descriptive board names and organize in folders

## Example Board Structure
```typescript
import { createBoard } from '@wixc3/react-board';
import { ComponentWrapper } from '_codux/wrappers/component-wrapper';
import { designTokens } from '_codux/design-system-setup';
import { MyComponent } from '~/components/my-component/my-component';

export default createBoard({
  name: 'MyComponent',
  Board: () => (
    <ComponentWrapper>
      <div style={{ padding: designTokens.spacing.xl }}>
        <MyComponent />
      </div>
    </ComponentWrapper>
  ),
  environmentProps: {
    windowWidth: 800,
    windowHeight: 600,
  },
  isSnippet: true,
});
```

This setup ensures all your Codux boards have consistent access to the design system and can properly preview components with the correct styling.
