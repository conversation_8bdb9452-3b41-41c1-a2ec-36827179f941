import { createBoard } from '@wixc3/react-board';
import { ComponentWrapper } from '_codux/wrappers/component-wrapper';
import { designTokens, withDesignSystem } from '_codux/design-system-setup';

// Example component that uses design system tokens
const DesignSystemExample = () => {
  return (
    <div style={{
      padding: designTokens.spacing['2xl'],
      backgroundColor: designTokens.colors.primary.medium,
      borderRadius: designTokens.borderRadius.xl,
      boxShadow: designTokens.shadows.card,
      maxWidth: '400px',
      margin: '0 auto',
    }}>
      <h2 style={{
        fontFamily: designTokens.fontFamily.heading,
        fontSize: designTokens.fontSize['2xl'],
        fontWeight: designTokens.fontWeight.black,
        color: designTokens.colors.accent.green,
        marginBottom: designTokens.spacing.lg,
        background: 'linear-gradient(135deg, #2d5016 0%, #4a148c 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        backgroundClip: 'text',
      }}>
        Design System Component
      </h2>
      
      <p style={{
        fontFamily: designTokens.fontFamily.primary,
        fontSize: designTokens.fontSize.base,
        color: designTokens.colors.text.primary,
        lineHeight: '1.6',
        marginBottom: designTokens.spacing.xl,
      }}>
        This component demonstrates how to use design system tokens in Codux boards.
      </p>
      
      <button style={{
        fontFamily: designTokens.fontFamily.mono,
        fontSize: designTokens.fontSize.sm,
        fontWeight: designTokens.fontWeight.semibold,
        padding: `${designTokens.spacing.md} ${designTokens.spacing.xl}`,
        backgroundColor: 'transparent',
        background: 'linear-gradient(135deg, #2d5016 0%, #4a148c 100%)',
        color: designTokens.colors.neutral.white,
        border: 'none',
        borderRadius: designTokens.borderRadius['2xl'],
        cursor: 'pointer',
        transition: designTokens.transitions.all,
        boxShadow: designTokens.shadows.md,
      }}>
        Design System Button
      </button>
      
      <div style={{
        marginTop: designTokens.spacing.xl,
        padding: designTokens.spacing.lg,
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        borderRadius: designTokens.borderRadius.md,
        border: '1px solid rgba(255, 255, 255, 0.2)',
      }}>
        <h3 style={{
          fontFamily: designTokens.fontFamily.mono,
          fontSize: designTokens.fontSize.sm,
          color: designTokens.colors.text.secondary,
          marginBottom: designTokens.spacing.sm,
        }}>
          Available Design Tokens:
        </h3>
        <ul style={{
          fontFamily: designTokens.fontFamily.mono,
          fontSize: designTokens.fontSize.xs,
          color: designTokens.colors.text.muted,
          listStyle: 'none',
          padding: 0,
          margin: 0,
        }}>
          <li>• Colors: primary, accent, neutral, text, status</li>
          <li>• Spacing: xs (4px) to 7xl (80px)</li>
          <li>• Typography: 3 font families, 10 sizes, 7 weights</li>
          <li>• Shadows: sm to 2xl + card variants</li>
          <li>• Border radius: sm to full</li>
          <li>• Transitions: fast, normal, slow</li>
        </ul>
      </div>
    </div>
  );
};

export default createBoard({
  name: 'Design System Example',
  Board: () => (
    <ComponentWrapper>
      <DesignSystemExample />
    </ComponentWrapper>
  ),
  environmentProps: {
    windowWidth: 800,
    windowHeight: 600,
  },
  isSnippet: true,
});
