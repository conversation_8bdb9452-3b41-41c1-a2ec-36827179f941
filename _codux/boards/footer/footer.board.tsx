import { Footer } from '../../../src/components/footer/footer';
import { ContentSlot, createBoard } from '@wixc3/react-board';
import { ComponentWrapper } from '_codux/wrappers/component-wrapper';
import styles from './footer.board.module.scss';

export default createBoard({
    name: 'Footer',
    Board: () => (
        <ComponentWrapper>
            <ContentSlot>
                <Footer className={styles.footer} />
            </ContentSlot>
        </ComponentWrapper>
    ),
});
