import { Smallcard } from '../../../src/components/smallcard/smallcard';
import { ContentSlot, createBoard } from '@wixc3/react-board';
import { ComponentWrapper } from '_codux/wrappers/component-wrapper';
import { designTokens } from '_codux/design-system-setup';

export default createBoard({
    name: 'Smallcard',
    Board: () => (
        <ComponentWrapper>
            <div style={{
                padding: designTokens.spacing.xl,
                backgroundColor: designTokens.colors.primary.dark,
                minHeight: '400px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}>
                <ContentSlot>
                    <Smallcard />
                </ContentSlot>
            </div>
        </ComponentWrapper>
    ),
    environmentProps: {
        windowWidth: 800,
        windowHeight: 600,
    },
    isSnippet: true,
});
