// Design System Setup for Codux Boards
// This file ensures all design system variables and mixins are available in Codux

import React from 'react';

// Import all design system files in the correct order
import '~/styles/colors.scss';
import '~/styles/global.scss';
import '~/styles/utils.scss';

// Export design tokens as JavaScript objects for use in boards
export const designTokens = {
  colors: {
    primary: {
      dark: '#0f0f23',
      medium: '#1a1a2e',
      light: '#16213e',
    },
    accent: {
      green: '#2d5016',
      greenLight: '#61c21b',
      purple: '#4a148c',
      purpleLight: '#6a1b9a',
      blue: '#0e19b8',
    },
    neutral: {
      white: '#ffffff',
      black: '#000000',
      gray100: '#f8fafc',
      gray200: '#e5e7eb',
      gray300: '#d1d5db',
      gray400: '#9ca3af',
      gray500: '#6b7280',
      gray600: '#4b5563',
      gray700: '#374151',
      gray800: '#1f2937',
      gray900: '#111827',
    },
    text: {
      primary: '#a8a8a8',
      secondary: '#9a9a9a',
      light: 'rgba(255, 255, 255, 0.9)',
      muted: 'rgba(255, 255, 255, 0.8)',
    },
    status: {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '32px',
    '4xl': '40px',
    '5xl': '48px',
    '6xl': '64px',
    '7xl': '80px',
  },
  borderRadius: {
    sm: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
    '2xl': '25px',
    full: '50px',
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px',
    '4xl': '36px',
    '5xl': '48px',
    '6xl': '60px',
  },
  fontWeight: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  fontFamily: {
    primary: "'DM Sans', sans-serif",
    heading: "'Audiowide', system-ui",
    mono: "'Monaco', monospace",
  },
  shadows: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1)',
    '2xl': '0 25px 50px rgba(0, 0, 0, 0.25)',
    card: '0 4px 15px rgba(0, 0, 0, 0.1)',
    cardHover: '0 8px 25px rgba(0, 0, 0, 0.15)',
  },
  transitions: {
    fast: '0.15s ease',
    normal: '0.2s ease',
    slow: '0.3s ease',
    all: 'all 0.2s ease',
  },
};

// Utility functions for boards
export const getSpacing = (size: keyof typeof designTokens.spacing) => designTokens.spacing[size];
export const getColor = (category: keyof typeof designTokens.colors, shade?: string) => {
  const colorCategory = designTokens.colors[category];
  if (typeof colorCategory === 'string') return colorCategory;
  if (shade && typeof colorCategory === 'object') {
    return (colorCategory as any)[shade];
  }
  return colorCategory;
};
export const getFontSize = (size: keyof typeof designTokens.fontSize) => designTokens.fontSize[size];
export const getShadow = (size: keyof typeof designTokens.shadows) => designTokens.shadows[size];

// Common component props for boards
export const commonBoardProps = {
  style: {
    fontFamily: designTokens.fontFamily.primary,
    backgroundColor: designTokens.colors.primary.dark,
    color: designTokens.colors.text.primary,
    minHeight: '100vh',
    padding: designTokens.spacing.lg,
  },
};

// Board wrapper with design system context
export const withDesignSystem = (Component: React.ComponentType) => {
  return (props: any) => (
    <div style={commonBoardProps.style}>
      <Component {...props} />
    </div>
  );
};
