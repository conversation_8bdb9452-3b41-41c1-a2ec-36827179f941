import '../../../src/styles/typography.scss';
import '../../../src/styles/global.scss';
import { LinksFunction, LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { getUrlOriginWithPath } from '~/utils';
import styles0 from './route.module.scss';
import classNames from 'classnames';
import { CardBlog } from '../../../src/components/card-blog/card-blog';
import { SectionGrid } from '../../../src/components/section-grid/section-grid';
import { FormContact } from '../../../src/components/form-contact/form-contact';
import Iframescreenshot040920251110Png from '../../../src/assets/iframescreenshot - 04:09:2025, 11:10.png';
import Group82Png from '../../../src/assets/group 82.png';
import { Smallcard } from '../../../src/components/smallcard/smallcard';
import { Footer } from '../../../src/components/footer/footer';
import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import cardBlogStyles from '../../../src/components/card-blog/card-blog.module.scss';

gsap.registerPlugin(ScrollTrigger);

export const loader = ({ request }: LoaderFunctionArgs) => {
    return { canonicalUrl: getUrlOriginWithPath(request.url) };
};

export default function HomePage() {
    const headerRef = useRef<HTMLHeadingElement>(null);
    const subtitleRef = useRef<HTMLHeadingElement>(null);
    const descriptionRef = useRef<HTMLParagraphElement>(null);
    const projectsHeaderRef = useRef<HTMLHeadingElement>(null);
    const paragraphRef = useRef<HTMLParagraphElement>(null);
    const cardsGridRef = useRef<HTMLDivElement>(null);
    const footerRef = useRef<HTMLDivElement>(null);
    const heroImgRef = useRef<HTMLImageElement>(null);
    const heroBtnRef = useRef<HTMLButtonElement>(null);
    const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

    useEffect(() => {
        // Sequential timeline for hero subtitle, title, description
        const tl = gsap.timeline();
        if (subtitleRef.current) {
            tl.fromTo(
                subtitleRef.current,
                { y: -30, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' }
            );
        }
        if (headerRef.current) {
            tl.fromTo(
                headerRef.current,
                { y: -60, opacity: 0, scale: 0.8 },
                { y: 0, opacity: 1, scale: 1, duration: 1, ease: 'power3.out' },
                '-=0.3'
            );
        }
        if (descriptionRef.current) {
            tl.fromTo(
                descriptionRef.current,
                { y: 30, opacity: 0 },
                { y: 0, opacity: 1, duration: 0.8, ease: 'power2.out' },
                '-=0.5'
            );
        }
        // GSAP pulse animation for hero button on hover
        if (heroBtnRef.current) {
            heroBtnRef.current.addEventListener('mouseenter', () => {
                gsap.to(heroBtnRef.current, {
                    scale: 1.12,
                    duration: 0.35,
                    ease: 'elastic.out(1, 0.5)',
                });
            });
            heroBtnRef.current.addEventListener('mouseleave', () => {
                gsap.to(heroBtnRef.current, {
                    scale: 1,
                    duration: 0.3,
                    ease: 'power2.out',
                });
            });
        }
        // Animate hero image
        if (heroImgRef.current) {
            gsap.fromTo(
                heroImgRef.current,
                { opacity: 0, scale: 0.85, x: 80 },
                {
                    opacity: 1,
                    scale: 1,
                    x: 0,
                    duration: 1.2,
                    delay: 0.7,
                    ease: 'power2.out',
                    scrollTrigger: {
                        trigger: heroImgRef.current,
                        start: 'top 85%',
                    },
                }
            );
        }
        if (projectsHeaderRef.current) {
            gsap.fromTo(
                projectsHeaderRef.current,
                { x: -80, opacity: 0 },
                {
                    x: 0,
                    opacity: 1,
                    duration: 1.2,
                    delay: 0.3,
                    ease: 'power2.out',
                    scrollTrigger: {
                        trigger: projectsHeaderRef.current,
                        start: 'top 80%',
                    },
                }
            );
        }
        if (paragraphRef.current) {
            gsap.fromTo(
                paragraphRef.current,
                { y: 40, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1,
                    delay: 0.6,
                    ease: 'power2.out',
                    scrollTrigger: {
                        trigger: paragraphRef.current,
                        start: 'top 85%',
                    },
                }
            );
        }
        // Animate cards with stagger and springy hover effect
        if (cardRefs.current.length) {
            gsap.fromTo(
                cardRefs.current,
                { y: 40, opacity: 0, scale: 0.95 },
                {
                    y: 0,
                    opacity: 1,
                    scale: 1,
                    duration: 1.1,
                    ease: 'power3.out',
                    stagger: 0.18,
                    scrollTrigger: {
                        trigger: cardsGridRef.current,
                        start: 'top 80%',
                    },
                }
            );
            cardRefs.current.forEach((card) => {
                if (card) {
                    card.addEventListener('mouseenter', () => {
                        gsap.to(card, {
                            scale: 1.06,
                            duration: 0.32,
                            ease: 'elastic.out(1, 0.5)',
                        });
                    });
                    card.addEventListener('mouseleave', () => {
                        gsap.to(card, {
                            scale: 1,
                            duration: 0.28,
                            ease: 'power2.out',
                        });
                    });
                }
            });
        }
        if (footerRef.current) {
            gsap.fromTo(
                footerRef.current,
                { y: 60, opacity: 0 },
                {
                    y: 0,
                    opacity: 1,
                    duration: 1.2,
                    delay: 1,
                    ease: 'power2.out',
                    scrollTrigger: {
                        trigger: footerRef.current,
                        start: 'top 90%',
                    },
                }
            );
        }
    }, []);

    return (
        <div className={styles0.div7}>
            <>
                <div className={styles0.grid3}>
                    <img src={Group82Png} className={styles0.img4} alt="Group 82" />
                    <nav className={styles0.nav2}>
                        <a href="/home">Home</a> | <a href="/projects">Projects</a> |{' '}
                        <a href="/about">About</a> | <a href="/contact">Contact Us</a>
                    </nav>
                </div>
                {/* Hero Section */}
                <section className={classNames(styles0.heroSection, styles0.section1)}>
                    <div className={classNames(styles0.heroLeft, styles0.div4)}>
                        <h1 ref={headerRef} className={styles0.header1}>
                            Welcome to My React Project
                        </h1>
                        <p
                            ref={descriptionRef}
                            className={classNames(styles0.heroParagraph, styles0.p3)}
                        >
                            This is a modern hero section with two columns. On the left, you can
                            introduce your project, product, or yourself. On the right, you can
                            display an engaging image to capture attention.
                        </p>
                        <button ref={heroBtnRef} className={classNames('button', styles0.button1)}>
                            Chekni moje projekty
                        </button>
                    </div>
                    <div className={classNames(styles0.heroRight, styles0.div5)}>
                        <img
                            ref={heroImgRef}
                            src={Iframescreenshot040920251110Png}
                            alt="Hero"
                            className={styles0.img3}
                            height=""
                            width=""
                        />
                    </div>
                </section>

                {/* Cards Section */}
                <section className={classNames(styles0.cardsSection, styles0.section2)}>
                    <div className={styles0.div6}>
                        <h1
                            ref={projectsHeaderRef}
                            className={classNames(styles0.Headcard, styles0.header3)}
                        >
                            My projects
                        </h1>
                        <p ref={paragraphRef} className={classNames(styles0.p4, styles0.p1)}>
                            This is a placeholder paragraph. It is here to provide structure w
                        </p>
                        <Smallcard className={styles0.smallcard} />
                        <div ref={cardsGridRef} className={styles0.grid}>
                            {[0, 1, 2].map((i) => (
                                <div key={i} ref={(el) => (cardRefs.current[i] = el)}>
                                    <CardBlog className={cardBlogStyles.Headcard} />
                                </div>
                            ))}
                        </div>
                    </div>
                    {/* New Section: Grid of Features */}
                    <SectionGrid
                        title="Features"
                        subtitle="What makes this project awesome"
                        columns={3}
                        gap="md"
                        centered
                        className={styles0.sectionGrid}
                    >
                        <div className={styles0.div8}>
                            <h3>Fast & Modern</h3>
                            <p>
                                Built with Vite, React, and Codux for instant reloads and modern DX.
                            </p>
                        </div>
                        <div>
                            <h3>Beautiful UI</h3>
                            <p>Unified gold accent, custom cards, and smooth GSAP animations.</p>
                        </div>
                        <div>
                            <h3>Easy Customization</h3>
                            <p>Modular components and SCSS for rapid design changes.</p>
                        </div>
                    </SectionGrid>
                    {/* New Section: Contact Form */}
                    <FormContact onSubmit={() => {}} className={styles0.formContact} />
                    <div ref={footerRef}>
                        <Footer />
                    </div>
                </section>
            </>
        </div>
    );
}

export const meta: MetaFunction<typeof loader> = ({ data }) => {
    const title = 'Blank Starter';
    const description = 'Welcome to the Blank Starter';
    const imageUrl = 'https://website-starter.com/og-image.png';

    return [
        { title },
        {
            name: 'description',
            content: description,
        },
        {
            tagName: 'link',
            rel: 'canonical',
            href: data?.canonicalUrl,
        },
        {
            property: 'robots',
            content: 'index, follow',
        },
        {
            property: 'og:title',
            content: title,
        },
        {
            property: 'og:description',
            content: description,
        },
        {
            property: 'og:image',
            content: imageUrl,
        },
        {
            name: 'twitter:card',
            content: 'summary_large_image',
        },
        {
            name: 'twitter:title',
            content: title,
        },
        {
            name: 'twitter:description',
            content: description,
        },
        {
            name: 'twitter:image',
            content: imageUrl,
        },
    ];
};

export const links: LinksFunction = () => {
    return [
        {
            rel: 'icon',
            href: '/favicon.ico',
            type: 'image/ico',
        },
    ];
};
