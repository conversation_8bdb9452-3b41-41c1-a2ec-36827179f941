@import '../../../src/styles/mixins.scss';

// Hardcoded design system variables for Codux compatibility
// Colors
$primary-dark: #0f0f23;
$primary-medium: #1a1a2e;
$primary-light: #16213e;
$accent-green: #2d5016;
$accent-green-light: #61c21b;
$accent-purple: #4a148c;
$accent-purple-light: #6a1b9a;
$white: #ffffff;
$black: #000000;
$gray-100: #f8fafc;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$text-primary: #a8a8a8;
$text-secondary: #9a9a9a;
$text-light: rgba(255, 255, 255, 0.9);
$text-muted: rgba(255, 255, 255, 0.8);
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #3b82f6;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-2xl: 24px;
$spacing-3xl: 32px;
$spacing-4xl: 40px;
$spacing-5xl: 48px;
$spacing-6xl: 64px;
$spacing-7xl: 80px;

// Typography
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;
$font-size-5xl: 48px;
$font-size-6xl: 60px;

$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

$line-height-tight: 1.1;
$line-height-snug: 1.2;
$line-height-normal: 1.3;
$line-height-relaxed: 1.4;
$line-height-loose: 1.6;

$font-primary: 'DM Sans', sans-serif;
$font-heading: 'Audiowide', system-ui;
$font-mono: 'Monaco', monospace;

// Border Radius
$radius-sm: 8px;
$radius-md: 12px;
$radius-lg: 16px;
$radius-xl: 20px;
$radius-2xl: 25px;
$radius-full: 50px;

// Shadows
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
$shadow-card: 0 4px 15px rgba(0, 0, 0, 0.1);
$shadow-card-hover: 0 8px 25px rgba(0, 0, 0, 0.15);

// Transitions
$transition-fast: 0.15s ease;
$transition-normal: 0.2s ease;
$transition-slow: 0.3s ease;
$transition-all: all 0.2s ease;

// Gradients
$gradient-primary: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);
$gradient-primary-hover: linear-gradient(135deg, #{$accent-green-light} 0%, #{$accent-purple-light} 100%);
$gradient-card: linear-gradient(180deg, #{$primary-medium} 0%, #{$primary-light} 100%);
$gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-text: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);

// Responsive mixins for Codux
@mixin mobile-only {
    @media (max-width: 767px) {
        @content;
    }
}

@mixin tablet-up {
    @media (min-width: 768px) {
        @content;
    }
}

@mixin desktop-up {
    @media (min-width: 1024px) {
        @content;
    }
}

@include mobile-only {
    .section1 {
        flex-direction: column;
        padding-top: $spacing-4xl;
        align-items: center;
    }
    .div4 {
        width: 100%;
        padding-top: $spacing-xl;
        padding-left: 0;
        padding-right: 0;
    }
    .img3 {
        max-width: 90vw;
        height: auto;
        margin: 0 auto;
    }
    .header1 {
        font-size: $font-size-3xl;
        padding-left: 0;
    }
    .p3 {
        padding-left: 0;
        padding-right: 0;
        font-size: $font-size-base;
    }
    .grid {
        grid-template-columns: 1fr;
        margin-left: 0;
        margin-right: 0;
        gap: $spacing-lg;
    }
    .grid3 {
        grid-template-columns: 1fr;
        padding-left: 0;
        padding-top: $spacing-md;
        justify-items: center;
    }
    .img4 {
        width: 60vw;
        min-width: 120px;
        margin: 0 auto 16px auto;
    }
    .section {
        padding: 30px 0;
    }
    .div1 {
        margin: 20px 0;
        min-height: 300px;
        padding: 20px 10px;
    }
    .button1 {
        margin-left: 0;
        min-width: 120px;
        font-size: 1rem;
        padding: 8px 12px;
    }
}
@media (max-width: 600px) {
    .header1 {
        font-size: 1.3rem;
    }
    .section {
        padding: 18px 0;
    }
    .div1 {
        min-height: 180px;
        padding: 10px 2px;
    }
    .img4 {
        width: 90vw;
        min-width: 80px;
    }
    .grid3 {
        gap: 8px;
    }
}
.div1 {
    max-width: 400px;
    display: flex;
    flex-direction: column;
    padding: 30px 20px;
    flex-wrap: wrap;
    min-height: 500px;
    background-color: #dddddd;
    margin: 50px;
    border-radius: 25px;
    &:hover {
        box-shadow: 3.54px 3.54px 11px #a1a7b5;
    }
}
.button1 {
    max-width: 50%;
    border-radius: 25px;
    background-color: #72729b;
    font-weight: 600;
    color: #ffffff;
    min-height: 25px;
    padding: 5px 15px;
    border: 2px solid #035b42;
    display: block;
    font-family: Monaco, monospace;
    margin-top: 20px;
    margin-left: 50px;
    &:hover:not(:disabled) {
        transform: translateY(-2px);
    }

    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }
}
.p1 {
    margin-bottom: 20px;
    font-family: Monaco, monospace;
    font-size: 16px;
}
.img1 {
    margin-bottom: 20px;
    border-radius: 15px;
}
.header1 {
    // slightly bigger than default
    font-weight: 900; // bold for titles
    line-height: 1.3;
    margin-bottom: 20px;
    color: #2c2c54; // dark indigo to match .button1 theme
    letter-spacing: -0.5px;

    background: repeat padding-box border-box 34% 55% / 145% 145% scroll
        linear-gradient(90deg, #ffffff 18%, #183a80 59%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: Audiowide, system-ui;
    font-size: 3rem;
    padding-left: 50px;
}
.div2 {
    object-fit: fill;
    display: block;
    width: 100%;
    min-width: 1920px;
    min-height: 1300px;
    gap: 10px;
    flex-direction: column;
}
.div3 {
    padding: 20px;
}
.header2 {
    line-height: 12px;
    font-size: 10px;
    font-weight: 300;
}
.section1 {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
}
.section2 {
    display: block;
    background: none;
    padding: 60px 0 0 0;
    min-height: unset;
}

.section {
    padding: 60px 0;
    width: 100%;
    box-sizing: border-box;
}
.div4 {
    width: 300%;
    padding-top: 60px;
    padding-right: 0px;
}
.img2 {
    width: 500px;
}
.heroimage {
    max-height: 390px;
    height: 390px;
}
.p2 {
    display: flex;
    flex-wrap: wrap;
}
.img3 {
    object-fit: scale-down;
    max-width: 100%;
    height: auto;
    display: block;

    /* start hidden */
    opacity: 0;

    /* run animation after 10s */
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
.div5 {
    display: flex;
}
.p3 {
    font-size: 16px;
    font-family: Monaco, monospace;
    color: #b3b3b3;
    padding-left: 50px;
    padding-right: 100px;
    text-align: start;
}
.Headcard {
    // slightly bigger than default
    font-weight: 900; // bold for titles
    line-height: 1.3;
    margin-bottom: 20px;
    color: #2c2c54; // dark indigo to match .button1 theme
    letter-spacing: -0.5px;

    background: linear-gradient(90deg, #4946a7, #6366f1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: Audiowide, system-ui;
    font-size: 1.7rem;
}

.grid {
    display: grid;
    grid-template: 1fr / 1fr 1fr 1fr;
    margin-top: 100px;
    gap: 20px;
    margin-left: 30px;
    margin-right: 30px;

    /* initial state */
    opacity: 0;
    transform: translateY(50px);

    /* animation */
    animation: slideUp 1s ease-out forwards; /* adjust delay if needed */
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grid1 {
    display: grid;
    grid-template: 1fr / 1fr 1fr;
    gap: 20px;
}

.grid2 {
    display: grid;
    grid-template: 1fr / 1fr 1fr;
    gap: 20px;
}

.grid3 {
    display: grid;
    grid-template: 1fr / 1fr 1fr;
    gap: 20px;
    background-repeat: no-repeat;
    background-image: none;
    background-position: 50%;
    background-size: cover;
    background-attachment: scroll;
    background-origin: padding-box;
    background-clip: border-box;
    background-color: rgba(15, 14, 14, 0.29);
    justify-items: start;
    align-items: center;
    padding-top: 15px;
    padding-left: 50px;
}
.img4 {
    width: 25%;
    display: block;
    overflow: visible;

    /* pulse animation */
    animation: pulse 2s ease-in-out infinite;
    transform-origin: center;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
.nav1 {
    color: #ffffff;
}
.nav2 {
    color: var(--white);
}
.div6 {
    display: flex;
    flex-direction: column;
}
.p4 {
    color: #b3b3b3;
    text-align: center;
}
.header3 {
    color: #b3b3b3;
    text-align: center;
}
.div7 {
    overflow: visible;
    background-repeat: no-repeat;
    background-image: url('../../../src/assets/rectangle 69.png');
    background-position: 50%;
    background-size: 100%;
    background-attachment: scroll;
    background-origin: padding-box;
    background-clip: border-box;
    background-color: transparent;
}
// Smallcard styling handled by component
.sectionGrid {
    margin-top: $spacing-7xl;
}
// div8 styling handled elsewhere
.div9 {
    display: flex;
    justify-content: center;
}
