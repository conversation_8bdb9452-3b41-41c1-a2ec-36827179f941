{"name": "user-card", "private": true, "sideEffects": false, "type": "module", "version": "1.0.0", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "typecheck": "tsc"}, "dependencies": {"@netlify/remix-adapter": "^2.6.0", "@remix-run/node": "^2.15.3", "@remix-run/react": "^2.15.3", "@types/node": "^22.13.1", "classnames": "^2.5.1", "gsap": "^3.13.0", "isbot": "^5.1.22", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@remix-run/dev": "^2.15.3", "@remix-run/testing": "^2.15.3", "@types/react": "^18.3.13", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^7.13.1", "@wixc3/define-remix-app": "^4.6.2", "@wixc3/react-board": "^4.6.2", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "sass": "^1.83.4", "typescript": "^5.7.3", "vite": "^5.4.11", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=18.0.0"}}