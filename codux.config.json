{"$schema": "https://wixplosives.github.io/codux-config-schema/codux.config.schema.json", "boardGlobalSetup": "./_codux/board-global-setup.ts", "componentsDiscovery": {"include": ["src/**"], "exclude": ["_codux/component-templates/**"]}, "boardsPath": "_codux/boards", "newComponent": {"componentsPath": "src/components", "templatesPath": "_codux/component-templates"}, "newBoard": {"templatesPath": "_codux/board-wrappers"}, "safeRender": {"maxInstancesPerComponent": 1000}, "scripts": {"install": {"title": "Install", "description": "Run install", "command": "npm i", "trigger": ["checkout", "pull", "setup"]}}, "styling": {"solution": "scss modules"}, "styleFilesConfig": {"commonStyleFilePattern": "**/src/styles/**"}, "resolve": {"alias": {"@styles": "./src/styles", "@styles/*": "./src/styles/*", "/*": "./*", "~/*": "./src/*", "node:fs": false, "node:fs/promises": false, "node:path": false, "node:crypto": false, "crypto": false, "node:stream": false, "stream": false, "node:os": false, "node:util": false, "node:events": false, "node:url": false, "node:buffer": false, "node:assert": false}}, "svgLoader": "both"}