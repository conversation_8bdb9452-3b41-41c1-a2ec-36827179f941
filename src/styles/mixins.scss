@import './design-tokens.scss';

// Design System Mixins
// Reusable styling patterns for consistent component design

// Card Base Mixin
@mixin card-base {
    display: flex;
    flex-direction: column;
    padding: $card-padding;
    background: $gradient-card;
    border-radius: $radius-2xl;
    box-shadow: $shadow-card;
    transition: $transition-all;
    min-height: $card-min-height;
    max-width: $card-max-width;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-card-hover;
    }
}

// Button Base Mixin
@mixin button-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: $font-mono;
    font-weight: $font-weight-semibold;
    border: none;
    border-radius: $radius-2xl;
    cursor: pointer;
    transition: $transition-all;
    text-decoration: none;
    
    &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
    }
}

// Primary Button
@mixin button-primary {
    @include button-base;
    background: $gradient-primary;
    color: $white;
    padding: $button-padding-md;
    
    &:hover:not(:disabled) {
        background: $gradient-primary-hover;
        transform: translateY(-2px) scale(1.02);
        box-shadow: $shadow-lg;
    }
}

// Secondary Button
@mixin button-secondary {
    @include button-base;
    background: $glass-background;
    color: $white;
    border: $glass-border;
    backdrop-filter: $glass-backdrop-filter;
    padding: $button-padding-md;
    
    &:hover:not(:disabled) {
        background: $glass-background-hover;
        transform: translateY(-2px);
    }
}

// Ghost Button
@mixin button-ghost {
    @include button-base;
    background: transparent;
    color: $text-primary;
    border: 2px solid $gray-300;
    padding: $button-padding-md;

    &:hover:not(:disabled) {
        background: $gray-100;
        border-color: $gray-400;
        transform: translateY(-1px);
    }
}

// Small Button Variant
@mixin button-small {
    padding: $button-padding-sm;
    font-size: $font-size-sm;
    border-radius: $radius-lg;
}

// Large Button Variant
@mixin button-large {
    padding: $button-padding-lg;
    font-size: $font-size-lg;
    border-radius: $radius-2xl;
}

// Link Button (looks like a link but behaves like a button)
@mixin button-link {
    @include button-base;
    background: none;
    border: none;
    color: $accent-green;
    text-decoration: underline;
    padding: 0;
    border-radius: 0;

    &:hover:not(:disabled) {
        color: $accent-purple;
        text-decoration: none;
        transform: none;
    }

    @include focus-ring;
}

// Danger Button
@mixin button-danger {
    @include button-base;
    background: $error;
    color: $white;
    padding: $button-padding-md;

    &:hover:not(:disabled) {
        background: darken($error, 10%);
        transform: translateY(-2px) scale(1.02);
        box-shadow: $shadow-lg;
    }
}

// Success Button
@mixin button-success {
    @include button-base;
    background: $success;
    color: $white;
    padding: $button-padding-md;

    &:hover:not(:disabled) {
        background: darken($success, 10%);
        transform: translateY(-2px) scale(1.02);
        box-shadow: $shadow-lg;
    }
}

// Input Base Mixin
@mixin input-base {
    padding: $input-padding;
    border: $input-border-width solid $gray-200;
    border-radius: $radius-md;
    font-family: $font-mono;
    font-size: $font-size-base;
    background-color: $gray-100;
    transition: $transition-all;
    
    &:focus {
        outline: none;
        border-color: $accent-purple;
        background-color: $white;
        box-shadow: 0 0 0 3px rgba(74, 20, 140, 0.1);
    }
    
    &::placeholder {
        color: $gray-400;
    }
    
    &.error {
        border-color: $error;
        background-color: #fef2f2;
        
        &:focus {
            border-color: $error;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
    }
}

// Text Gradient Mixin
@mixin text-gradient {
    background: $gradient-text;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: $accent-green; // fallback
}

// Heading Base Mixin
@mixin heading-base {
    @include text-gradient;
    font-family: $font-heading;
    font-weight: $font-weight-black;
    line-height: $line-height-tight;
    margin-bottom: $spacing-lg;
}

// Section Container Mixin
@mixin section-container {
    max-width: $container-xl;
    margin: 0 auto;
    padding: 0 $spacing-xl;
    width: 100%;
}

// Glassmorphism Card Mixin
@mixin glass-card {
    background: $glass-background;
    backdrop-filter: $glass-backdrop-filter;
    border: $glass-border;
    border-radius: $radius-xl;
    box-shadow: $shadow-lg;
    
    &:hover {
        background: $glass-background-hover;
    }
}

// Responsive Typography Mixin
@mixin responsive-text($mobile-size, $desktop-size) {
    font-size: $mobile-size;
    
    @media (min-width: $breakpoint-md) {
        font-size: $desktop-size;
    }
}

// Flex Center Mixin
@mixin flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

// Absolute Center Mixin
@mixin absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

// Truncate Text Mixin
@mixin truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

// Visually Hidden Mixin
@mixin visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

// Focus Ring Mixin
@mixin focus-ring {
    &:focus {
        outline: 2px solid $accent-purple;
        outline-offset: 2px;
    }
}

// Hover Lift Effect
@mixin hover-lift($lift: 2px, $scale: 1.02) {
    transition: $transition-all;

    &:hover {
        transform: translateY(-#{$lift}) scale(#{$scale});
    }
}

// Interactive Element Base
@mixin interactive-element {
    cursor: pointer;
    transition: $transition-all;
    user-select: none;

    &:hover {
        @include hover-lift;
    }

    &:active {
        transform: translateY(1px) scale(0.98);
    }

    @include focus-ring;
}

// Clickable Card
@mixin clickable-card {
    @include card-base;
    @include interactive-element;

    &:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: $shadow-xl;
    }
}

// Animated Icon
@mixin animated-icon {
    transition: $transition-all;

    &:hover {
        transform: scale(1.1) rotate(5deg);
    }
}

// Pulse Animation
@mixin pulse-animation {
    animation: pulse 2s infinite;

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
}

// Fade In Animation
@mixin fade-in($duration: 0.3s, $delay: 0s) {
    opacity: 0;
    animation: fadeIn $duration ease-in-out $delay forwards;

    @keyframes fadeIn {
        to {
            opacity: 1;
        }
    }
}

// Slide In Animation
@mixin slide-in($direction: 'up', $distance: 20px, $duration: 0.3s, $delay: 0s) {
    opacity: 0;

    @if $direction == 'up' {
        transform: translateY($distance);
    } @else if $direction == 'down' {
        transform: translateY(-$distance);
    } @else if $direction == 'left' {
        transform: translateX($distance);
    } @else if $direction == 'right' {
        transform: translateX(-$distance);
    }

    animation: slideIn#{capitalize($direction)} $duration ease-out $delay forwards;

    @if $direction == 'up' {
        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    } @else if $direction == 'down' {
        @keyframes slideInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    } @else if $direction == 'left' {
        @keyframes slideInLeft {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    } @else if $direction == 'right' {
        @keyframes slideInRight {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    }
}

// Media Query Mixins
@mixin mobile-only {
    @media (max-width: #{$breakpoint-md - 1px}) {
        @content;
    }
}

@mixin tablet-up {
    @media (min-width: $breakpoint-md) {
        @content;
    }
}

@mixin desktop-up {
    @media (min-width: $breakpoint-lg) {
        @content;
    }
}

@mixin large-desktop-up {
    @media (min-width: $breakpoint-xl) {
        @content;
    }
}
