/* <PERSON> CSS Reset 
 * Source: https://www.joshwcomeau.com/css/custom-css-reset/
 * Description: A modern CSS reset to improve consistency and control over default styles.
 */

/* 1. Use a more-intuitive box-sizing model */
*,
*::before,
*::after {
}
/* 2. Remove default margin */
* {
    margin: 0;
}
body {
    /* 3. Add accessible line-height */
    line-height: 1.5;
    /* 4. Improve text rendering */
    -webkit-font-smoothing: antialiased;
}
/* 5. Improve media defaults */
img,
picture,
video,
canvas,
svg {
    display: block;
    max-width: 100%;
}
/* 6. Inherit fonts for form controls */
input,
button,
textarea,
select {
    font: inherit;
}
/* 7. Avoid text overflows */
p,
h1,
h2,
h3,
h4,
h5,
h6 {
    overflow-wrap: break-word;
}
/* 8. Improve line wrapping */
p {
    text-wrap: pretty;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    text-wrap: balance;
}

/* Custom additions to the original <PERSON> reset by <PERSON>dux */

a {
    text-decoration: none;
    color: inherit;
}
