@import './design-tokens.scss';
@import './mixins.scss';

// Typography System
// Consistent typography scale and utilities

// Font Face Imports (moved to global.scss for better organization)

// Typography Utilities
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }
.text-5xl { font-size: $font-size-5xl; }
.text-6xl { font-size: $font-size-6xl; }

// Font Weight Utilities
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }
.font-extrabold { font-weight: $font-weight-extrabold; }
.font-black { font-weight: $font-weight-black; }

// Font Family Utilities
.font-primary { font-family: $font-primary; }
.font-heading { font-family: $font-heading; }
.font-mono { font-family: $font-mono; }

// Line Height Utilities
.leading-tight { line-height: $line-height-tight; }
.leading-snug { line-height: $line-height-snug; }
.leading-normal { line-height: $line-height-normal; }
.leading-relaxed { line-height: $line-height-relaxed; }
.leading-loose { line-height: $line-height-loose; }

// Text Color Utilities
.text-primary { color: $text-primary; }
.text-secondary { color: $text-secondary; }
.text-light { color: $text-light; }
.text-muted { color: $text-muted; }
.text-white { color: $white; }

// Text Alignment
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// Text Transform
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

// Text Decoration
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

// Letter Spacing
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

// Word Break
.break-normal { word-break: normal; overflow-wrap: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

// Whitespace
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

// Text Overflow
.truncate {
    @include truncate;
}

// Selection
::selection {
    background-color: rgba(74, 20, 140, 0.2);
    color: $accent-purple;
}

// Legacy CSS Custom Properties (for backward compatibility)
:root {
    --font-primary: #{$font-primary};
    --font-heading: #{$font-heading};
    --font-mono: #{$font-mono};
    --large-font: #{$font-weight-bold} #{$font-size-5xl}/#{$line-height-tight} var(--font-heading);
    --small-font: #{$font-weight-light} #{$font-size-lg}/#{$line-height-relaxed} var(--font-primary);
    --paragraph-font: #{$font-weight-normal} #{$font-size-base}/#{$line-height-normal} var(--font-primary);
}
