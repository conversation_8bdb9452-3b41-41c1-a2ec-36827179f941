# Design System Documentation

This design system provides consistent styling across all components in the application. It includes design tokens, mixins, utility classes, and component patterns.

## File Structure

- `colors.scss` - Color palette and gradients
- `design-tokens.scss` - Spacing, typography, shadows, and other design tokens
- `mixins.scss` - Reusable styling patterns and mixins
- `typography.scss` - Typography utilities and text styling
- `global.scss` - Global styles, button variants, and utility classes
- `reset.scss` - CSS reset styles
- `utils.scss` - Additional utility classes

## Design Tokens

### Colors
- **Primary**: Dark theme colors (`$primary-dark`, `$primary-medium`, `$primary-light`)
- **Accents**: Green and purple gradients (`$accent-green`, `$accent-purple`)
- **Neutrals**: Gray scale from 100-900
- **Status**: Success, warning, error, info colors
- **Text**: Primary, secondary, light, and muted text colors

### Typography
- **Font Families**: 
  - Primary: 'DM Sans' (body text)
  - Heading: 'Audiowide' (headings)
  - Mono: 'Monaco' (code/technical text)
- **Font Sizes**: xs (12px) to 6xl (60px)
- **Font Weights**: light (300) to black (900)
- **Line Heights**: tight (1.1) to loose (1.6)

### Spacing
- **Scale**: xs (4px) to 7xl (80px)
- **Usage**: Consistent spacing for margins, padding, gaps

### Shadows
- **Scale**: sm to 2xl
- **Special**: Card shadows with hover variants

## Component Mixins

### Cards
```scss
@include card-base; // Standard card styling
@include glass-card; // Glassmorphism effect
@include clickable-card; // Interactive card with hover effects
```

### Buttons
```scss
@include button-primary; // Main action buttons
@include button-secondary; // Secondary actions
@include button-ghost; // Outline buttons
@include button-link; // Link-style buttons
@include button-danger; // Destructive actions
@include button-success; // Positive actions
```

### Button Sizes
```scss
@include button-small; // Compact buttons
@include button-large; // Prominent buttons
```

### Forms
```scss
@include input-base; // Standard input styling
```

### Typography
```scss
@include heading-base; // Consistent heading styles
@include text-gradient; // Gradient text effect
@include responsive-text($mobile, $desktop); // Responsive font sizes
```

### Layout
```scss
@include section-container; // Page section wrapper
@include flex-center; // Center content with flexbox
@include absolute-center; // Absolute positioning center
```

### Interactive Elements
```scss
@include interactive-element; // Basic interactive styling
@include hover-lift($lift, $scale); // Hover lift effect
@include focus-ring; // Accessibility focus ring
```

### Animations
```scss
@include fade-in($duration, $delay); // Fade in animation
@include slide-in($direction, $distance, $duration, $delay); // Slide animations
@include pulse-animation; // Pulse effect
```

## Utility Classes

### Layout
- `.container` - Section container with max-width
- `.flex-center` - Center content with flexbox
- `.glass-card` - Glassmorphism card effect

### Typography
- `.text-gradient` - Gradient text effect
- `.text-xs` to `.text-6xl` - Font sizes
- `.font-light` to `.font-black` - Font weights
- `.leading-tight` to `.leading-loose` - Line heights
- `.text-left`, `.text-center`, `.text-right` - Text alignment
- `.uppercase`, `.lowercase`, `.capitalize` - Text transform
- `.truncate` - Text overflow ellipsis

### Interactive
- `.interactive-element` - Basic interactive styling
- `.hover-lift` - Hover lift effect
- `.animated-icon` - Icon hover animation
- `.clickable-card` - Interactive card

### Buttons
- `.button-secondary`, `.button-ghost`, `.button-link` - Button variants
- `.button-danger`, `.button-success` - Status buttons
- `.button-small`, `.button-large` - Button sizes

### Animations
- `.fade-in`, `.slide-in-up`, `.slide-in-down` - Animation classes
- `.pulse` - Pulse animation

### Responsive
- `.mobile-only` - Hide on tablet and up
- `.tablet-up` - Hide on mobile

## Usage Examples

### Creating a Card Component
```scss
.my-card {
  @include card-base;
  
  .title {
    @include heading-base;
    @include responsive-text($font-size-lg, $font-size-2xl);
  }
  
  .button {
    @include button-primary;
    @include button-small;
  }
}
```

### Responsive Typography
```scss
.hero-title {
  @include heading-base;
  @include responsive-text($font-size-3xl, $font-size-6xl);
}
```

### Interactive Elements
```scss
.clickable-item {
  @include interactive-element;
  @include hover-lift(4px, 1.05);
}
```

## Best Practices

1. **Use design tokens** instead of hardcoded values
2. **Prefer mixins** over duplicating styles
3. **Use utility classes** for simple styling
4. **Follow the spacing scale** for consistent layouts
5. **Use semantic color names** rather than specific hex values
6. **Test responsive behavior** with the responsive mixins
7. **Ensure accessibility** with focus rings and proper contrast

## Migration Guide

When updating existing components:

1. Replace hardcoded colors with design tokens
2. Use button mixins instead of custom button styles
3. Apply card mixins for consistent card styling
4. Use typography mixins for headings and text
5. Replace custom hover effects with standard mixins
6. Add responsive behavior with media query mixins

This design system ensures consistency, maintainability, and scalability across the entire application.
