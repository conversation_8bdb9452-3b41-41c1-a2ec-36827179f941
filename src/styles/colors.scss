// Design System Colors
// Primary Colors
$primary-dark: #0f0f23;
$primary-medium: #1a1a2e;
$primary-light: #16213e;

// Accent Colors
$accent-green: #2d5016;
$accent-green-light: #61c21b;
$accent-purple: #4a148c;
$accent-purple-light: #6a1b9a;
$accent-blue: #0e19b8;

// Neutral Colors
$white: #ffffff;
$black: #000000;
$gray-100: #f8fafc;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Text Colors
$text-primary: #a8a8a8;
$text-secondary: #9a9a9a;
$text-light: rgba(255, 255, 255, 0.9);
$text-muted: rgba(255, 255, 255, 0.8);

// Status Colors
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #3b82f6;

// Gradients
$gradient-primary: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);
$gradient-primary-hover: linear-gradient(135deg, #{$accent-green-light} 0%, #{$accent-purple-light} 100%);
$gradient-card: linear-gradient(180deg, #{$primary-medium} 0%, #{$primary-light} 100%);
$gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-text: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);

// Legacy support (for backward compatibility)
$secondary: $gray-500;
$primary: $primary-medium;
$accent-gold: #ce5353;
$accent-gold-solid: $accent-green;
