// Design System Tokens
// This file contains all design tokens for consistent styling across components

// Design System Colors
// Primary Colors
$primary-dark: #0f0f23;
$primary-medium: #1a1a2e;
$primary-light: #16213e;

// Accent Colors
$accent-green: #2d5016;
$accent-green-light: #61c21b;
$accent-purple: #4a148c;
$accent-purple-light: #6a1b9a;
$accent-blue: #0e19b8;

// Neutral Colors
$white: #ffffff;
$black: #000000;
$gray-100: #f8fafc;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Text Colors
$text-primary: #a8a8a8;
$text-secondary: #9a9a9a;
$text-light: rgba(255, 255, 255, 0.9);
$text-muted: rgba(255, 255, 255, 0.8);

// Status Colors
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #3b82f6;

// Gradients
$gradient-primary: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);
$gradient-primary-hover: linear-gradient(135deg, #{$accent-green-light} 0%, #{$accent-purple-light} 100%);
$gradient-card: linear-gradient(180deg, #{$primary-medium} 0%, #{$primary-light} 100%);
$gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-text: linear-gradient(135deg, #{$accent-green} 0%, #{$accent-purple} 100%);

// Legacy support (for backward compatibility)
$secondary: $gray-500;
$primary: $primary-medium;
$accent-gold: #ce5353;
$accent-gold-solid: $accent-green;

// Spacing Scale
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-2xl: 24px;
$spacing-3xl: 32px;
$spacing-4xl: 40px;
$spacing-5xl: 48px;
$spacing-6xl: 64px;
$spacing-7xl: 80px;

// Border Radius
$radius-sm: 8px;
$radius-md: 12px;
$radius-lg: 16px;
$radius-xl: 20px;
$radius-2xl: 25px;
$radius-full: 50px;

// Shadows
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
$shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);
$shadow-card: 0 4px 15px rgba(0, 0, 0, 0.1);
$shadow-card-hover: 0 8px 25px rgba(0, 0, 0, 0.15);

// Typography Scale
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;
$font-size-5xl: 48px;
$font-size-6xl: 60px;

// Font Weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// Line Heights
$line-height-tight: 1.1;
$line-height-snug: 1.2;
$line-height-normal: 1.3;
$line-height-relaxed: 1.4;
$line-height-loose: 1.6;

// Font Families
$font-primary: 'DM Sans', sans-serif;
$font-heading: 'Audiowide', system-ui;
$font-mono: 'Monaco', monospace;

// Transitions
$transition-fast: 0.15s ease;
$transition-normal: 0.2s ease;
$transition-slow: 0.3s ease;
$transition-all: all 0.2s ease;

// Z-Index Scale
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Container Sizes
$container-sm: 640px;
$container-md: 768px;
$container-lg: 1024px;
$container-xl: 1280px;
$container-2xl: 1400px;

// Component Specific Tokens
$card-padding: $spacing-2xl $spacing-xl;
$card-min-height: 500px;
$card-max-width: 400px;

$button-padding-sm: $spacing-sm $spacing-lg;
$button-padding-md: $spacing-md $spacing-2xl;
$button-padding-lg: $spacing-lg $spacing-3xl;

$input-padding: $spacing-md $spacing-lg;
$input-border-width: 2px;

// Glassmorphism Effects
$glass-background: rgba(255, 255, 255, 0.1);
$glass-background-hover: rgba(255, 255, 255, 0.2);
$glass-border: 1px solid rgba(255, 255, 255, 0.2);
$glass-backdrop-filter: blur(10px);

// Animation Curves
$ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out-cubic: cubic-bezier(0, 0, 0.2, 1);
$ease-in-cubic: cubic-bezier(0.4, 0, 1, 1);
