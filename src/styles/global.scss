@import './reset.scss';
@import './mixins.scss';
@import './typography.scss';
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100;0,9..40,200;0,9..40,300;0,9..40,400;0,9..40,500;0,9..40,600;0,9..40,700;0,9..40,800;0,9..40,900;0,9..40,1000;1,9..40,100;1,9..40,200;1,9..40,300;1,9..40,400;1,9..40,500;1,9..40,600;1,9..40,700;1,9..40,800;1,9..40,900;1,9..40,1000&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Audiowide&display=swap');

:root {
    font-family: $font-primary;
    line-height: $line-height-normal;
    font-weight: $font-weight-normal;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
    background-color: $primary-dark;
    color: $text-primary;
    font-family: $font-primary;
    line-height: $line-height-relaxed;
}

#root {
    /* create a stacking context for the app */
    isolation: isolate;
}

// Typography Hierarchy
h1, h2, h3, h4, h5, h6 {
    @include heading-base;
}

h1 {
    @include responsive-text($font-size-4xl, $font-size-6xl);
}

h2 {
    @include responsive-text($font-size-3xl, $font-size-5xl);
}

h3 {
    @include responsive-text($font-size-2xl, $font-size-4xl);
}

h4 {
    @include responsive-text($font-size-xl, $font-size-3xl);
}

h5 {
    @include responsive-text($font-size-lg, $font-size-2xl);
}

h6 {
    @include responsive-text($font-size-base, $font-size-xl);
}

// Global Button Styles
button, .button1, .button {
    @include button-primary;

    // Legacy class support
    &.button1, &.button {
        font-family: $font-mono;
    }
}

// Button Variants
.button-secondary {
    @include button-secondary;
}

.button-ghost {
    @include button-ghost;
}

.button-link {
    @include button-link;
}

.button-danger {
    @include button-danger;
}

.button-success {
    @include button-success;
}

// Button Sizes
.button-small {
    @include button-small;
}

.button-large {
    @include button-large;
}

// Link Styles
a {
    color: $accent-green;
    text-decoration: underline;
    font-weight: $font-weight-semibold;
    transition: $transition-normal;

    &:hover {
        color: $accent-purple;
        text-decoration: none;
    }

    @include focus-ring;
}

// Form Elements
input, textarea, select {
    @include input-base;
}

// Utility Classes
.text-gradient {
    @include text-gradient;
}

.flex-center {
    @include flex-center;
}

.container {
    @include section-container;
}

.glass-card {
    @include glass-card;
}

.clickable-card {
    @include clickable-card;
}

.interactive-element {
    @include interactive-element;
}

.hover-lift {
    @include hover-lift;
}

.animated-icon {
    @include animated-icon;
}

.pulse {
    @include pulse-animation;
}

.fade-in {
    @include fade-in;
}

.slide-in-up {
    @include slide-in('up');
}

.slide-in-down {
    @include slide-in('down');
}

.slide-in-left {
    @include slide-in('left');
}

.slide-in-right {
    @include slide-in('right');
}

// Spacing Utilities
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }

.p-0 { padding: 0; }

// Display Utilities
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

// Position Utilities
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// Width/Height Utilities
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// Overflow Utilities
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

// Cursor Utilities
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

// User Select Utilities
.select-none { user-select: none; }
.select-text { user-select: text; }

// Responsive utilities
.mobile-only {
    @include tablet-up {
        display: none !important;
    }
}

.tablet-up {
    @include mobile-only {
        display: none !important;
    }
}

// Print utilities
@media print {
    .print-hidden {
        display: none !important;
    }
}
