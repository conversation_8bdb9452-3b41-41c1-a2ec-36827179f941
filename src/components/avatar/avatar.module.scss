@import '../../styles/mixins.scss';

.avatar {
    position: relative;
    @include flex-center;
    overflow: hidden;
    background-color: $gray-200;
    border: 2px solid $white;
    box-shadow: $shadow-md;
}

// Sizes
.xs {
    width: 24px;
    height: 24px;
    font-size: $font-size-xs;
}

.sm {
    width: 32px;
    height: 32px;
    font-size: $font-size-xs;
}

.md {
    width: 48px;
    height: 48px;
    font-size: $font-size-base;
}

.lg {
    width: 64px;
    height: 64px;
    font-size: $font-size-xl;
}

.xl {
    width: 96px;
    height: 96px;
    font-size: $font-size-3xl;
}

// Shapes
.circle {
    border-radius: $radius-full;
}

.square {
    border-radius: $radius-sm;
}

.image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fallback {
    width: 100%;
    height: 100%;
    @include flex-center;
    background: $gradient-hero;
    color: $white;
    font-weight: $font-weight-semibold;
    font-family: $font-mono;
}

.clickable {
    @include interactive-element;

    &:hover {
        transform: scale(1.05);
    }
}

.status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 25%;
    height: 25%;
    border-radius: $radius-full;
    border: 2px solid $white;
    min-width: 8px;
    min-height: 8px;
}

.online {
    background-color: $success;
}

.offline {
    background-color: $gray-500;
}

.busy {
    background-color: $error;
}

.away {
    background-color: $warning;
}
