import styles from './avatar.module.scss';
import cx from 'classnames';

export interface AvatarProps {
    className?: string;
    src: string;
    alt: string;
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    shape?: 'circle' | 'square';
    status?: 'online' | 'offline' | 'busy' | 'away';
    showStatus?: boolean;
    fallbackText?: string;
    onClick?: () => void;
}

/**
 * Reusable Avatar component with different sizes, shapes, and status indicators
 */
export const Avatar = ({
    className,
    src,
    alt,
    size = 'md',
    shape = 'circle',
    status,
    showStatus = false,
    fallbackText,
    onClick,
}: AvatarProps) => {
    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map((word) => word.charAt(0))
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return null;
};
