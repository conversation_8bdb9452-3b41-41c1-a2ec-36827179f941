.div1 {
    max-width: 320px;
    display: flex;
    flex-direction: column;
    padding: 25px;
    background-color: transparent;
    margin: 20px;
    border-radius: 20px;
    background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 280px;
    &:hover {
        outline: none;
        border: none;
        transform: translateY(-3px);
        transition: all 0.3s ease;
    }
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.nameSection {
    margin-left: 15px;
    flex: 1;
}

.img1 {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.Headcard {
    font-weight: 700;
    line-height: 1.2;
    color: #ffffff;
    font-family: Audiowide, system-ui;
    font-size: 1.3rem;
    margin: 0;
    display: flex;
    align-items: center;
}

.verified {
    background-color: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 8px;
}

.statsContainer {
    display: flex;
    justify-content: space-between;
    margin-bottom: 25px;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.statNumber {
    font-size: 18px;
    font-weight: 800;
    color: #ffffff;
    font-family: Monaco, monospace;
    line-height: 1;
}

.statLabel {
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    font-family: Monaco, monospace;
    margin-top: 2px;
}

.button1 {
    border-radius: 15px;
    background-color: #ffffff;
    font-weight: 600;
    color: #764ba2;
    padding: 12px 24px;
    border: none;
    font-family: Monaco, monospace;
    cursor: pointer;
    margin-top: auto;
    &:hover {
        background-color: #f8f9fa;
        transform: scale(1.02);
    }
}
