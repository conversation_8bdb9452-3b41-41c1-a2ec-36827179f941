import '../../styles/utils.scss';
import styles from './card-user-stats.module.scss';
import cx from 'classnames';

export interface CardUserStatsProps {
    className?: string;
    name: string;
    avatar: string;
    stats: {
        followers: number;
        following: number;
        posts: number;
    };
    isVerified?: boolean;
    onFollow?: () => void;
}

/**
 * User stats card component for social media style profiles
 */
export const CardUserStats = ({ 
    className, 
    name, 
    avatar,
    stats,
    isVerified = false,
    onFollow 
}: CardUserStatsProps) => {
    return (
        <div className={cx(styles.card, styles.root, className)}>
            <div className={styles.div1}>
                <div className={styles.header}>
                    <img
                        src={avatar}
                        alt={name}
                        className={styles.img1}
                    />
                    <div className={styles.nameSection}>
                        <h2 className={styles.Headcard}>
                            {name}
                            {isVerified && <span className={styles.verified}>✓</span>}
                        </h2>
                    </div>
                </div>
                
                <div className={styles.statsContainer}>
                    <div className={styles.stat}>
                        <span className={styles.statNumber}>{stats.posts.toLocaleString()}</span>
                        <span className={styles.statLabel}>Posts</span>
                    </div>
                    <div className={styles.stat}>
                        <span className={styles.statNumber}>{stats.followers.toLocaleString()}</span>
                        <span className={styles.statLabel}>Followers</span>
                    </div>
                    <div className={styles.stat}>
                        <span className={styles.statNumber}>{stats.following.toLocaleString()}</span>
                        <span className={styles.statLabel}>Following</span>
                    </div>
                </div>

                <button 
                    className={styles.button1} 
                    onClick={onFollow} 
                    type="button"
                >
                    Follow +
                </button>
            </div>
        </div>
    );
};
