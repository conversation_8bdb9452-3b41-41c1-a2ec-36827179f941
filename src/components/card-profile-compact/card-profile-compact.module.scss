.div1 {
    max-width: 300px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20px;
    background-color: transparent;
    margin: 20px;
    border-radius: 15px;
    background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 120px;
    &:hover {
        outline: none;
        border: none;
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
}

.content {
    margin-left: 15px;
    flex: 1;
}

.button1 {
    border-radius: 15px;
    background-color: #764ba2;
    font-weight: 600;
    color: #ffffff;
    padding: 8px 16px;
    border: none;
    font-family: Monaco, monospace;
    font-size: 12px;
    cursor: pointer;
    margin-top: 8px;
}

.img1 {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.Headcard {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 5px;
    color: #ffffff;
    font-family: Audiowide, system-ui;
    font-size: 1.1rem;
    margin: 0;
}

.Smallinfo {
    font-size: 11px;
    font-weight: 300;
    font-family: Monaco, monospace;
    color: #e0e0e0;
    margin: 0 0 8px 0;
}
