import '../../styles/utils.scss';
import styles from './card-profile-compact.module.scss';
import cx from 'classnames';

export interface CardProfileCompactProps {
    className?: string;
    name: string;
    title: string;
    imageUrl: string;
    onViewProfile?: () => void;
}

/**
 * Compact profile card component for smaller spaces
 */
export const CardProfileCompact = ({ 
    className, 
    name, 
    title, 
    imageUrl, 
    onViewProfile 
}: CardProfileCompactProps) => {
    return (
        <div className={cx(styles.card, styles.root, className)}>
            <div className={styles.div1}>
                <img
                    src={imageUrl}
                    alt={name}
                    className={styles.img1}
                />
                <div className={styles.content}>
                    <h3 className={styles.Headcard}>{name}</h3>
                    <p className={styles.Smallinfo}>{title}</p>
                    <button 
                        className={styles.button1} 
                        onClick={onViewProfile} 
                        type="button"
                    >
                        Connect
                    </button>
                </div>
            </div>
        </div>
    );
};
