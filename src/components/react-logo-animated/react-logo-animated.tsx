import styles from './react-logo-animated.module.scss';
import cx from 'classnames';
import { useEffect, useState } from 'react';

export interface ReactLogoAnimatedProps {
    className?: string;
    size?: 'sm' | 'md' | 'lg' | 'xl';
    autoStart?: boolean;
    onAnimationComplete?: () => void;
}

/**
 * Animated React logo component for page loading and welcome screens
 */
export const ReactLogoAnimated = ({
    className,
    size = 'lg',
    autoStart = true,
    onAnimationComplete
}: ReactLogoAnimatedProps) => {
    const [isAnimating, setIsAnimating] = useState(autoStart);
    const [showContent, setShowContent] = useState(false);

    useEffect(() => {
        if (isAnimating) {
            // Show the logo after a brief delay
            const showTimer = setTimeout(() => {
                setShowContent(true);
            }, 200);

            // Call completion callback after animation
            const completeTimer = setTimeout(() => {
                if (onAnimationComplete) {
                    onAnimationComplete();
                }
            }, 8000); // Increased from 3000 to 8000ms

            return () => {
                clearTimeout(showTimer);
                clearTimeout(completeTimer);
            };
        }
    }, [isAnimating, onAnimationComplete]);

    const startAnimation = () => {
        setIsAnimating(true);
        setShowContent(false);
        setTimeout(() => setShowContent(true), 200);
    };

    return (
        <div className={cx(styles.container, styles[size], className)}>
            {showContent && (
                <div className={styles.perspective}>
                    <div className={styles.reactLogo}>
                        {/* Electron orbits */}
                        <div className={cx(styles.orbit, styles.orbit1)}>
                            <div className={styles.electron}></div>
                        </div>
                        <div className={cx(styles.orbit, styles.orbit2)}>
                            <div className={styles.electron}></div>
                        </div>
                        <div className={cx(styles.orbit, styles.orbit3)}>
                            <div className={styles.electron}></div>
                        </div>
                        
                        {/* Nucleus */}
                        <div className={styles.nucleus}></div>
                        
                        {/* Glow effect */}
                        <div className={styles.glow}></div>
                    </div>
                </div>
            )}
            
            {!autoStart && (
                <button 
                    className={styles.playButton}
                    onClick={startAnimation}
                    type="button"
                >
                    ▶ Animate
                </button>
            )}
        </div>
    );
};
