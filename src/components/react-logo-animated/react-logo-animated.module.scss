@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes orbitGlow {
    0%, 100% {
        box-shadow: 
            0 0 15px rgba(99, 102, 241, 0.5),
            inset 0 0 15px rgba(99, 102, 241, 0.2);
        border-color: rgba(99, 102, 241, 0.8);
    }
    50% {
        box-shadow: 
            0 0 30px rgba(99, 102, 241, 0.9),
            inset 0 0 25px rgba(99, 102, 241, 0.4);
        border-color: rgba(139, 92, 246, 1);
    }
}

.container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.perspective {
    perspective: 1000px;
    perspective-origin: center center;
}

.reactLogo {
    position: relative;
    animation: fadeIn 0.8s ease-out;
    transform-style: preserve-3d;
}

.orbit {
    position: absolute;
    border: 3px solid;
    border-radius: 50%;
    border-color: rgba(99, 102, 241, 0.8);
    background: transparent;
    box-shadow: 
        0 0 15px rgba(99, 102, 241, 0.5),
        inset 0 0 15px rgba(99, 102, 241, 0.2);
    animation: rotate 8s linear infinite, orbitGlow 4s ease-in-out infinite;
    transform-style: preserve-3d;
    
    &::before {
        content: '';
        position: absolute;
        inset: -1px;
        border-radius: 50%;
        border: 1px solid rgba(139, 92, 246, 0.6);
        background: transparent;
    }
}

.orbit1 {
    transform: rotateY(0deg) rotateX(75deg);
    animation-duration: 6s;
    border-color: rgba(99, 102, 241, 0.9);
}

.orbit2 {
    transform: rotateY(120deg) rotateX(75deg);
    animation-duration: 8s;
    animation-delay: -2s;
    border-color: rgba(139, 92, 246, 0.8);
}

.orbit3 {
    transform: rotateY(240deg) rotateX(75deg);
    animation-duration: 10s;
    animation-delay: -4s;
    border-color: rgba(168, 85, 247, 0.7);
}

.electron {
    position: absolute;
    background: radial-gradient(circle, #ffffff, #e2e8f0);
    border-radius: 50%;
    box-shadow: 
        0 0 20px rgba(255, 255, 255, 0.9),
        0 0 40px rgba(99, 102, 241, 0.5);
    animation: rotate 4s linear infinite reverse;
    z-index: 5;
}

.nucleus {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, #6366f1, #4338ca);
    border-radius: 50%;
    box-shadow: 
        0 0 30px rgba(99, 102, 241, 0.9),
        0 0 60px rgba(99, 102, 241, 0.6),
        inset 0 0 15px rgba(255, 255, 255, 0.3);
    animation: pulse 4s ease-in-out infinite;
    z-index: 10;
}

.glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, rgba(139, 92, 246, 0.2) 50%, transparent 70%);
    border-radius: 50%;
    animation: pulse 6s ease-in-out infinite;
    pointer-events: none;
}

.playButton {
    position: absolute;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-family: Monaco, monospace;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateX(-50%) translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }
}

// Size variations
.sm {
    .orbit {
        width: 80px;
        height: 80px;
    }
    .electron {
        width: 6px;
        height: 6px;
        top: -3px;
        left: 37px;
    }
    .nucleus {
        width: 12px;
        height: 12px;
    }
    .glow {
        width: 120px;
        height: 120px;
    }
}

.md {
    .orbit {
        width: 120px;
        height: 120px;
    }
    .electron {
        width: 8px;
        height: 8px;
        top: -4px;
        left: 56px;
    }
    .nucleus {
        width: 16px;
        height: 16px;
    }
    .glow {
        width: 180px;
        height: 180px;
    }
}

.lg {
    .orbit {
        width: 160px;
        height: 160px;
    }
    .electron {
        width: 10px;
        height: 10px;
        top: -5px;
        left: 75px;
    }
    .nucleus {
        width: 20px;
        height: 20px;
    }
    .glow {
        width: 240px;
        height: 240px;
    }
}

.xl {
    .orbit {
        width: 200px;
        height: 200px;
    }
    .electron {
        width: 12px;
        height: 12px;
        top: -6px;
        left: 94px;
    }
    .nucleus {
        width: 24px;
        height: 24px;
    }
    .glow {
        width: 300px;
        height: 300px;
    }
}

@media (max-width: 768px) {
    .lg, .xl {
        .orbit {
            width: 120px;
            height: 120px;
        }
        .electron {
            width: 8px;
            height: 8px;
            top: -4px;
            left: 56px;
        }
        .nucleus {
            width: 16px;
            height: 16px;
        }
        .glow {
            width: 180px;
            height: 180px;
        }
    }
}
