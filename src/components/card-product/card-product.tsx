import '../../styles/utils.scss';
import styles from './card-product.module.scss';
import cx from 'classnames';

export interface CardProductProps {
    className?: string;
}

/**
 * Product card component with similar structure to CardBlog
 */
export const CardProduct = ({ className }: CardProductProps) => {
    return (
        <div className={cx(styles.card, styles.root)}>
            <div className={styles.div1} key={null}>
                <h1 className={styles.Headcard}>Premium Headphones</h1>
                <img
                    src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt="Premium Headphones"
                    className={styles.img1}
                />
                <div>
                    <h2 className={styles.Smallinfo}>$299.99</h2>
                </div>
                <p className={styles.p1}>
                    Experience crystal-clear audio with our premium wireless headphones. 
                    Features noise cancellation, 30-hour battery life, and premium comfort.
                </p>
                <button className={styles.button1} onClick={undefined} type="button">
                    Buy Now &gt;
                </button>
            </div>
        </div>
    );
};
