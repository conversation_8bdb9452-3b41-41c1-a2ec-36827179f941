@import '../../styles/mixins.scss';

@include mobile-only {
    .div1 {
        max-width: 98vw;
        width: 100%;
        padding: $spacing-lg $spacing-sm;
        min-height: 220px;
        margin: $spacing-md auto;
        border-radius: $radius-md;
    }

    .Headcard {
        font-size: $font-size-lg;
        margin-bottom: $spacing-md;
    }

    .img1 {
        max-width: 90vw;
        height: auto;
        margin-bottom: $spacing-md;
    }

    .button1 {
        min-width: 100px;
        font-size: $font-size-base;
        padding: $spacing-sm $spacing-md;
        margin: $spacing-sm 0;
    }

    .p1 {
        font-size: 13px;
        margin-bottom: $spacing-md;
    }
}

.div1 {
    @include card-base;
    margin: $spacing-5xl;
}

.button1 {
    @include button-primary;
    max-width: 50%;
    min-height: 25px;
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
}

.p1 {
    margin-bottom: $spacing-xl;
    font-family: $font-mono;
    font-size: $font-size-base;
    color: $text-primary;
    line-height: $line-height-relaxed;
}

.img1 {
    margin-bottom: $spacing-xl;
    border-radius: $radius-lg;
    width: 100%;
    height: auto;
    object-fit: cover;
}
.Headcard {
    @include heading-base;
    @include responsive-text($font-size-lg, $font-size-2xl);
    letter-spacing: -0.5px;
}

.Smallinfo {
    font-size: $font-size-xs;
    font-weight: $font-weight-light;
    font-family: $font-mono;
    color: $text-secondary;
    margin-top: auto;
}
