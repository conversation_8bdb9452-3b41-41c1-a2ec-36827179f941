@import '../../styles/mixins.scss';

.root {
    @include flex-center;
    flex-direction: column;
    gap: $spacing-md;
    padding: $spacing-4xl;
    min-height: 50vh;
}

.heading1 {
    @include heading-base;
    font-size: $font-size-4xl;
    text-align: center;
    margin-bottom: $spacing-lg;
}

.link {
    color: $accent-purple;
    text-decoration: underline;
    font-weight: $font-weight-semibold;
    transition: $transition-normal;

    &:hover {
        color: $accent-purple-light;
        text-decoration: none;
    }

    @include focus-ring;
}
