import '../../styles/utils.scss';
import styles from './card-contact.module.scss';
import cx from 'classnames';

export interface CardContactProps {
    className?: string;
    name: string;
    title: string;
    company: string;
    avatar: string;
    email?: string;
    phone?: string;
    location?: string;
    onMessage?: () => void;
    onCall?: () => void;
}

/**
 * Contact card component for business networking
 */
export const CardContact = ({ 
    className, 
    name, 
    title,
    company,
    avatar,
    email,
    phone,
    location,
    onMessage,
    onCall
}: CardContactProps) => {
    return (
        <div className={cx(styles.card, styles.root, className)}>
            <div className={styles.div1}>
                <div className={styles.header}>
                    <img
                        src={avatar}
                        alt={name}
                        className={styles.img1}
                    />
                </div>
                
                <h2 className={styles.Headcard}>{name}</h2>
                <p className={styles.title}>{title}</p>
                <p className={styles.company}>{company}</p>
                
                <div className={styles.contactInfo}>
                    {email && (
                        <div className={styles.contactItem}>
                            <span className={styles.icon}>📧</span>
                            <span className={styles.contactText}>{email}</span>
                        </div>
                    )}
                    {phone && (
                        <div className={styles.contactItem}>
                            <span className={styles.icon}>📱</span>
                            <span className={styles.contactText}>{phone}</span>
                        </div>
                    )}
                    {location && (
                        <div className={styles.contactItem}>
                            <span className={styles.icon}>📍</span>
                            <span className={styles.contactText}>{location}</span>
                        </div>
                    )}
                </div>

                <div className={styles.buttonGroup}>
                    <button 
                        className={styles.button1} 
                        onClick={onMessage} 
                        type="button"
                    >
                        Message
                    </button>
                    <button 
                        className={styles.button2} 
                        onClick={onCall} 
                        type="button"
                    >
                        Call
                    </button>
                </div>
            </div>
        </div>
    );
};
