@import '../../styles/mixins.scss';

.div1 {
    @include card-base;
    max-width: 300px;
    align-items: center;
    text-align: center;
    margin: $spacing-xl;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 450px;
}

.header {
    margin-bottom: $spacing-xl;
}

.img1 {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.Headcard {
    font-weight: $font-weight-extrabold;
    line-height: $line-height-snug;
    margin-bottom: $spacing-sm;
    color: $white;
    font-family: $font-heading;
    font-size: $font-size-2xl;
}

.title {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    font-family: $font-mono;
    color: $white;
    margin: 0 0 $spacing-sm 0;
}

.company {
    font-size: 13px;
    font-weight: $font-weight-normal;
    font-family: $font-mono;
    color: $text-muted;
    margin: 0 0 $spacing-xl 0;
}

.contactInfo {
    width: 100%;
    margin-bottom: $spacing-2xl;
}

.contactItem {
    @include glass-card;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: $spacing-md;
    padding: $spacing-sm $spacing-md;
    border-radius: $radius-md;
}

.icon {
    margin-right: $spacing-md;
    font-size: $font-size-sm;
}

.contactText {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.9);
    font-family: $font-mono;
    flex: 1;
    text-align: left;
}

.buttonGroup {
    display: flex;
    gap: $spacing-md;
    width: 100%;
}

.button1, .button2 {
    @include button-base;
    flex: 1;
    border-radius: $radius-lg;
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-xs;
}

.button1 {
    background-color: $white;
    color: #0984e3;

    &:hover {
        background-color: $gray-100;
        transform: translateY(-1px);
    }
}

.button2 {
    @include button-secondary;
}
