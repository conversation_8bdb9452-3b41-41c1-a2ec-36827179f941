@import '../../styles/mixins.scss';

.section {
    padding: $spacing-7xl 0;
    background-color: $gray-100;
}

.container {
    @include section-container;
}

.header {
    margin-bottom: $spacing-6xl;
}

.header.centered {
    text-align: center;
}

.subtitle {
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    color: $accent-purple;
    margin-bottom: $spacing-md;
    font-family: $font-mono;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.title {
    @include heading-base;
    @include responsive-text($font-size-3xl, $font-size-5xl);
    line-height: $line-height-tight;
}

.description {
    @include responsive-text($font-size-base, $font-size-lg);
    color: $gray-500;
    line-height: $line-height-loose;
    font-family: $font-mono;
    max-width: 600px;
}

.header.centered .description {
    margin: 0 auto;
}

.grid {
    display: grid;
    width: 100%;
}

// Column variations
.cols-1 {
    grid-template-columns: 1fr;
}

.cols-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.cols-4 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

// Gap variations
.gap-sm {
    gap: $spacing-lg;
}

.gap-md {
    gap: $spacing-2xl;
}

.gap-lg {
    gap: $spacing-3xl;
}

.gap-xl {
    gap: $spacing-5xl;
}

@include mobile-only {
    .section {
        padding: $spacing-6xl 0;
    }

    .header {
        margin-bottom: $spacing-4xl;
    }

    .cols-2,
    .cols-3,
    .cols-4 {
        grid-template-columns: 1fr;
    }
}
