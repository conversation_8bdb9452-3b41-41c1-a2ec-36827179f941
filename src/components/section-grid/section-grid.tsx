import styles from './section-grid.module.scss';
import cx from 'classnames';
import { ReactNode } from 'react';

export interface SectionGridProps {
    className?: string;
    title?: string;
    subtitle?: string;
    children: ReactNode;
    columns?: 1 | 2 | 3 | 4;
    gap?: 'sm' | 'md' | 'lg';
    centered?: boolean;
}

/**
 * Grid section component for organizing content in responsive columns
 */
export const SectionGrid = ({ 
    className,
    title,
    subtitle,
    children,
    columns = 3,
    gap = 'md',
    centered = false
}: SectionGridProps) => {
    return (
        <section className={cx(styles.section, className)}>
            <div className={styles.container}>
                {(title || subtitle) && (
                    <div className={cx(styles.header, { [styles.centered]: centered })}>
                        {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
                        {title && <h2 className={styles.title}>{title}</h2>}
                    </div>
                )}
                
                <div className={cx(
                    styles.grid,
                    styles[`cols-${columns}`],
                    styles[`gap-${gap}`]
                )}>
                    {children}
                </div>
            </div>
        </section>
    );
};
