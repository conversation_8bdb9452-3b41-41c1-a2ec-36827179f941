.showcase {
    min-height: 100vh;
}

.avatarDemo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    p {
        font-size: 12px;
        font-weight: 500;
        color: #6b7280;
        font-family: Monaco, monospace;
        margin: 0;
    }
}

.formsSection {
    background-color: #f8fafc;
    padding: 80px 0;
}

.formWrapper {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.featureCard {
    background: white;
    padding: 32px 24px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    h3 {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c2c54;
        margin: 16px 0 12px 0;
        font-family: Audiowide, system-ui;
    }
    
    p {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.5;
        margin: 0;
        font-family: Monaco, monospace;
    }
}

.featureIcon {
    font-size: 2.5rem;
    margin-bottom: 8px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .formWrapper {
        width: 100%;
        justify-content: stretch;
    }
    
    .featureCard {
        padding: 24px 16px;
        
        h3 {
            font-size: 1.1rem;
        }
        
        p {
            font-size: 13px;
        }
    }
    
    .featureIcon {
        font-size: 2rem;
        height: 50px;
    }
}
