import { Avatar } from '../avatar/avatar';
import { SectionHero } from '../section-hero/section-hero';
import { SectionGrid } from '../section-grid/section-grid';
import { FormContact } from '../form-contact/form-contact';
import { FormLogin } from '../form-login/form-login';
import { CardBlog } from '../card-blog/card-blog';
import { CardProfile } from '../card-profile/card-profile';
import { CardProduct } from '../card-product/card-product';
import styles from './components-showcase.module.scss';

export interface ComponentsShowcaseProps {
    className?: string;
}

/**
 * Comprehensive showcase of all reusable components
 */
export const ComponentsShowcase = ({ className }: ComponentsShowcaseProps) => {
    const handleContactSubmit = (data: any) => {
        console.log('Contact form submitted:', data);
    };

    const handleLoginSubmit = (data: any) => {
        console.log('Login submitted:', data);
    };

    return (
        <div className={styles.showcase}>
            {/* Hero Section */}
            <SectionHero 
                title="Component Library Showcase"
                subtitle="Modern React Components"
                description="Explore our collection of beautiful, reusable React components built with TypeScript and SCSS."
                ctaText="Get Started"
                onCtaClick={() => console.log('CTA clicked')}
                alignment="center"
            />

            {/* Avatar Components */}
            <SectionGrid 
                title="Avatar Components"
                subtitle="Profile Pictures & Status"
                columns={4}
                gap="lg"
                centered
            >
                <div className={styles.avatarDemo}>
                    <Avatar 
                        src="https://images.unsplash.com/photo-1494790108755-2616b612b1e9?q=80&w=200"
                        alt="Sarah Johnson"
                        size="xs"
                        showStatus
                        status="online"
                    />
                    <p>Extra Small</p>
                </div>

                <div className={styles.avatarDemo}>
                    <Avatar 
                        src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=200"
                        alt="Alex Chen"
                        size="sm"
                        showStatus
                        status="busy"
                    />
                    <p>Small</p>
                </div>

                <div className={styles.avatarDemo}>
                    <Avatar 
                        src="https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=200"
                        alt="Michael Rodriguez"
                        size="md"
                        showStatus
                        status="away"
                    />
                    <p>Medium</p>
                </div>

                <div className={styles.avatarDemo}>
                    <Avatar 
                        src=""
                        alt="John Doe"
                        size="lg"
                        showStatus
                        status="offline"
                        shape="square"
                    />
                    <p>Large (Fallback)</p>
                </div>
            </SectionGrid>

            {/* Card Components */}
            <SectionGrid 
                title="Card Components"
                subtitle="Content Display"
                columns={3}
                gap="md"
                centered
            >
                <CardBlog />
                <CardProfile />
                <CardProduct />
            </SectionGrid>

            {/* Form Components */}
            <div className={styles.formsSection}>
                <SectionGrid 
                    title="Form Components"
                    subtitle="User Input & Authentication"
                    columns={2}
                    gap="lg"
                    centered
                >
                    <div className={styles.formWrapper}>
                        <FormLogin 
                            onSubmit={handleLoginSubmit}
                            onForgotPassword={() => console.log('Forgot password')}
                            onSignUp={() => console.log('Sign up')}
                        />
                    </div>

                    <div className={styles.formWrapper}>
                        <FormContact 
                            onSubmit={handleContactSubmit}
                        />
                    </div>
                </SectionGrid>
            </div>

            {/* Features Section */}
            <SectionGrid 
                title="Component Features"
                subtitle="What makes them special"
                columns={3}
                gap="md"
                centered
            >
                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>🎨</div>
                    <h3>Modern Design</h3>
                    <p>Beautiful gradients, smooth animations, and contemporary styling</p>
                </div>

                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>⚡</div>
                    <h3>TypeScript Ready</h3>
                    <p>Full type safety with comprehensive interfaces and props</p>
                </div>

                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>📱</div>
                    <h3>Responsive</h3>
                    <p>Mobile-first design that works on all screen sizes</p>
                </div>

                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>🔧</div>
                    <h3>Customizable</h3>
                    <p>Easy to customize colors, sizes, and behavior via props</p>
                </div>

                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>♿</div>
                    <h3>Accessible</h3>
                    <p>Built with accessibility in mind, keyboard navigation support</p>
                </div>

                <div className={styles.featureCard}>
                    <div className={styles.featureIcon}>🚀</div>
                    <h3>Production Ready</h3>
                    <p>Tested, optimized, and ready for real-world applications</p>
                </div>
            </SectionGrid>
        </div>
    );
};
