import styles from './form-login.module.scss';
import cx from 'classnames';
import { useState } from 'react';

export interface FormLoginProps {
    className?: string;
    title?: string;
    onSubmit: (data: LoginFormData) => void;
    onForgotPassword?: () => void;
    onSignUp?: () => void;
    isLoading?: boolean;
    showSocialLogin?: boolean;
}

export interface LoginFormData {
    email: string;
    password: string;
    rememberMe: boolean;
}

/**
 * Login form component with social login options
 */
export const FormLogin = ({ 
    className,
    title = "Welcome Back",
    onSubmit,
    onForgotPassword,
    onSignUp,
    isLoading = false,
    showSocialLogin = true
}: FormLoginProps) => {
    const [formData, setFormData] = useState<LoginFormData>({
        email: '',
        password: '',
        rememberMe: false
    });

    const [errors, setErrors] = useState<Partial<LoginFormData>>({});

    const validateForm = (): boolean => {
        const newErrors: Partial<LoginFormData> = {};

        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email';
        }

        if (!formData.password.trim()) {
            newErrors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            newErrors.password = 'Password must be at least 6 characters';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    const handleChange = (field: keyof LoginFormData) => (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        const value = field === 'rememberMe' ? e.target.checked : e.target.value;
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    return (
        <div className={cx(styles.formContainer, className)}>
            <div className={styles.header}>
                <h2 className={styles.title}>{title}</h2>
                <p className={styles.subtitle}>Please sign in to your account</p>
            </div>

            {showSocialLogin && (
                <div className={styles.socialLogin}>
                    <button type="button" className={cx(styles.socialButton, styles.google)}>
                        <span className={styles.socialIcon}>G</span>
                        Continue with Google
                    </button>
                    <button type="button" className={cx(styles.socialButton, styles.github)}>
                        <span className={styles.socialIcon}>⚡</span>
                        Continue with GitHub
                    </button>
                    
                    <div className={styles.divider}>
                        <span>or</span>
                    </div>
                </div>
            )}

            <form onSubmit={handleSubmit} className={styles.form}>
                <div className={styles.field}>
                    <label htmlFor="email" className={styles.label}>Email</label>
                    <input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange('email')}
                        className={cx(styles.input, { [styles.error]: errors.email })}
                        placeholder="<EMAIL>"
                    />
                    {errors.email && <span className={styles.errorText}>{errors.email}</span>}
                </div>

                <div className={styles.field}>
                    <label htmlFor="password" className={styles.label}>Password</label>
                    <input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={handleChange('password')}
                        className={cx(styles.input, { [styles.error]: errors.password })}
                        placeholder="Enter your password"
                    />
                    {errors.password && <span className={styles.errorText}>{errors.password}</span>}
                </div>

                <div className={styles.options}>
                    <label className={styles.checkbox}>
                        <input
                            type="checkbox"
                            checked={formData.rememberMe}
                            onChange={handleChange('rememberMe')}
                        />
                        <span className={styles.checkmark}></span>
                        Remember me
                    </label>
                    
                    {onForgotPassword && (
                        <button 
                            type="button" 
                            className={styles.linkButton}
                            onClick={onForgotPassword}
                        >
                            Forgot password?
                        </button>
                    )}
                </div>

                <button 
                    type="submit" 
                    className={styles.submitButton}
                    disabled={isLoading}
                >
                    {isLoading ? 'Signing in...' : 'Sign In'}
                </button>
            </form>

            {onSignUp && (
                <div className={styles.footer}>
                    <span>Don't have an account? </span>
                    <button 
                        type="button" 
                        className={styles.linkButton}
                        onClick={onSignUp}
                    >
                        Sign up
                    </button>
                </div>
            )}
        </div>
    );
};
