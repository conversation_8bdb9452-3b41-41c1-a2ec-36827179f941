@import '../../styles/mixins.scss';

.formContainer {
    max-width: 400px;
    margin: 0 auto;
    padding: $spacing-4xl;
    background: $white;
    border-radius: $radius-xl;
    box-shadow: $shadow-2xl;
}

.header {
    text-align: center;
    margin-bottom: $spacing-3xl;
}

.title {
    @include heading-base;
    font-size: $font-size-3xl;
    margin-bottom: $spacing-sm;

    @include tablet-up {
        font-size: $font-size-4xl;
    }
}

.subtitle {
    font-size: $font-size-sm;
    color: $gray-500;
    font-family: $font-mono;
}

.socialLogin {
    margin-bottom: $spacing-2xl;
}

.socialButton {
    @include button-ghost;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-md;
    margin-bottom: $spacing-md;
    color: $gray-700;

    &:last-child {
        margin-bottom: 0;
    }
}

.socialIcon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: $font-weight-bold;
}

.google {
    color: #ea4335;
    font-size: $font-size-base;
}

.github {
    color: $gray-800;
    font-size: $font-size-base;
}

.divider {
    text-align: center;
    margin: $spacing-2xl 0;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 1px;
        background: $gray-200;
    }

    span {
        background: $white;
        padding: 0 $spacing-lg;
        color: $gray-500;
        font-size: $font-size-sm;
        font-family: $font-mono;
    }
}

.form {
    display: flex;
    flex-direction: column;
    gap: $spacing-xl;
}

.field {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
}

.label {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    font-family: $font-mono;
}

.input {
    @include input-base;
}

.errorText {
    font-size: $font-size-xs;
    color: $error;
    font-family: $font-mono;
}

.options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: $spacing-sm 0;
}

.checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: $font-size-sm;
    color: $gray-700;
    font-family: $font-mono;

    input {
        display: none;
    }
}

.checkmark {
    width: 16px;
    height: 16px;
    border: 2px solid $gray-300;
    border-radius: $spacing-xs;
    margin-right: $spacing-sm;
    position: relative;

    input:checked + & {
        background-color: $accent-purple;
        border-color: $accent-purple;

        &::after {
            content: '✓';
            position: absolute;
            top: -2px;
            left: 2px;
            color: $white;
            font-size: $font-size-xs;
        }
    }
}

.linkButton {
    background: none;
    border: none;
    color: $accent-purple;
    font-size: $font-size-sm;
    font-family: $font-mono;
    cursor: pointer;
    text-decoration: underline;
    transition: $transition-normal;

    &:hover {
        color: $accent-purple-light;
    }
}

.submitButton {
    @include button-primary;
    width: 100%;
    margin-top: $spacing-sm;
    font-size: $font-size-base;
}

.footer {
    text-align: center;
    margin-top: $spacing-2xl;
    font-size: $font-size-sm;
    color: $gray-500;
    font-family: $font-mono;
}

@include mobile-only {
    .formContainer {
        padding: $spacing-3xl $spacing-xl;
        margin: $spacing-xl;
    }

    .title {
        font-size: $font-size-2xl;
    }
}
