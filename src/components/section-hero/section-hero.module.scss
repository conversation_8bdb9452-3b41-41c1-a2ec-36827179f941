@import '../../styles/mixins.scss';

.hero {
    position: relative;
    min-height: 80vh;
    @include flex-center;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-color: $gray-100;
    background-image: $gradient-hero;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: $z-dropdown;
}

.container {
    @include section-container;
    position: relative;
    z-index: $z-dropdown + 1;
}

.content {
    max-width: 800px;
    color: $white;
}

.left .content {
    margin-left: 0;
    text-align: left;
}

.center .content {
    margin: 0 auto;
    text-align: center;
}

.right .content {
    margin-right: 0;
    margin-left: auto;
    text-align: right;
}

.subtitle {
    @include responsive-text($font-size-base, $font-size-lg);
    font-weight: $font-weight-medium;
    margin-bottom: $spacing-lg;
    color: $text-light;
    font-family: $font-mono;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.title {
    @include responsive-text($font-size-4xl, $font-size-6xl);
    font-weight: $font-weight-black;
    margin-bottom: $spacing-2xl;
    line-height: $line-height-tight;
    font-family: $font-heading;
    background: linear-gradient(90deg, $white, #e2e8f0);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.description {
    @include responsive-text($font-size-lg, $font-size-xl);
    font-weight: $font-weight-normal;
    margin-bottom: $spacing-3xl;
    line-height: $line-height-loose;
    color: $text-light;
    font-family: $font-mono;
}

.cta {
    @include button-primary;
    padding: $spacing-lg $spacing-3xl;
    font-size: $font-size-lg;
    border-radius: $radius-full;
    box-shadow: $shadow-lg;

    &:hover {
        box-shadow: $shadow-xl;
    }
}

@include mobile-only {
    .hero {
        min-height: 60vh;
    }

    .subtitle {
        margin-bottom: $spacing-md;
    }

    .title {
        margin-bottom: $spacing-lg;
    }

    .description {
        margin-bottom: $spacing-2xl;
    }
}
