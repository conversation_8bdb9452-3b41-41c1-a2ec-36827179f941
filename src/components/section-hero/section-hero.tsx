import styles from './section-hero.module.scss';
import cx from 'classnames';

export interface SectionHeroProps {
    className?: string;
    title: string;
    subtitle?: string;
    description?: string;
    backgroundImage?: string;
    overlay?: boolean;
    ctaText?: string;
    onCtaClick?: () => void;
    alignment?: 'left' | 'center' | 'right';
}

/**
 * Hero section component for landing pages and headers
 */
export const SectionHero = ({
    className,
    title,
    subtitle,
    description,
    backgroundImage,
    overlay = true,
    ctaText,
    onCtaClick,
    alignment = 'center',
}: SectionHeroProps) => {
    return null;
};
