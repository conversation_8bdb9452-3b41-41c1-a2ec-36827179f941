@import '../../styles/mixins.scss';

.formContainer {
    max-width: 600px;
    margin: $spacing-5xl auto;
    padding: $spacing-4xl;
    background: $white;
    border-radius: $radius-xl;
    box-shadow: $shadow-card;
    border: 1px solid $gray-200;
    transition: $transition-all;

    &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-card-hover;
    }
}

.header {
    text-align: center;
    margin-bottom: $spacing-2xl;
}

.title {
    @include heading-base;
    font-size: $font-size-3xl;
    margin-bottom: $spacing-md;
    letter-spacing: -0.5px;

    @include tablet-up {
        font-size: $font-size-4xl;
    }
}

.description {
    font-size: $font-size-base;
    color: $gray-400;
    line-height: $line-height-loose;
    font-family: $font-mono;
}

.form {
    display: flex;
    flex-direction: column;
    gap: $spacing-2xl;
}

.row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;

    @include mobile-only {
        grid-template-columns: 1fr;
        gap: $spacing-lg;
    }
}

.field {
    display: flex;
    flex-direction: column;
    gap: $spacing-sm;
}

.label {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    font-family: $font-mono;
}

.input,
.textarea {
    @include input-base;
}

.textarea {
    resize: vertical;
    min-height: 120px;
}

.errorText {
    font-size: $font-size-xs;
    color: $error;
    font-family: $font-mono;
    margin-top: $spacing-xs;
}

.submitButton {
    @include button-primary;
    width: 100%;
    padding: $spacing-lg $spacing-3xl;
    margin-top: $spacing-lg;
}

@include mobile-only {
    .formContainer {
        max-width: 98vw;
        width: 100%;
        padding: $spacing-lg $spacing-sm;
        margin: $spacing-md auto;
        border-radius: $radius-md;
    }

    .title {
        font-size: $font-size-xl;
        margin-bottom: $spacing-md;
    }

    .description {
        font-size: $font-size-sm;
        margin-bottom: $spacing-sm;
    }
}
