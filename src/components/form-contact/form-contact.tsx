import styles from './form-contact.module.scss';
import cx from 'classnames';
import { useState } from 'react';

export interface FormContactProps {
    className?: string;
    title?: string;
    description?: string;
    onSubmit: (data: ContactFormData) => void;
    isLoading?: boolean;
}

export interface ContactFormData {
    name: string;
    email: string;
    subject: string;
    message: string;
}

/**
 * Contact form component with validation and modern styling
 */
export const FormContact = ({ 
    className,
    title = "Get in Touch",
    description = "We'd love to hear from you. Send us a message and we'll respond as soon as possible.",
    onSubmit,
    isLoading = false
}: FormContactProps) => {
    const [formData, setFormData] = useState<ContactFormData>({
        name: '',
        email: '',
        subject: '',
        message: ''
    });

    const [errors, setErrors] = useState<Partial<ContactFormData>>({});

    const validateForm = (): boolean => {
        const newErrors: Partial<ContactFormData> = {};

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
        }

        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email';
        }

        if (!formData.subject.trim()) {
            newErrors.subject = 'Subject is required';
        }

        if (!formData.message.trim()) {
            newErrors.message = 'Message is required';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    const handleChange = (field: keyof ContactFormData) => (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }));
        }
    };

    return (
        <div className={cx(styles.formContainer, className)}>
            <div className={styles.header}>
                <h2 className={styles.title}>{title}</h2>
                <p className={styles.description}>{description}</p>
            </div>

            <form onSubmit={handleSubmit} className={styles.form}>
                <div className={styles.row}>
                    <div className={styles.field}>
                        <label htmlFor="name" className={styles.label}>Name</label>
                        <input
                            id="name"
                            type="text"
                            value={formData.name}
                            onChange={handleChange('name')}
                            className={cx(styles.input, { [styles.error]: errors.name })}
                            placeholder="Your full name"
                        />
                        {errors.name && <span className={styles.errorText}>{errors.name}</span>}
                    </div>

                    <div className={styles.field}>
                        <label htmlFor="email" className={styles.label}>Email</label>
                        <input
                            id="email"
                            type="email"
                            value={formData.email}
                            onChange={handleChange('email')}
                            className={cx(styles.input, { [styles.error]: errors.email })}
                            placeholder="<EMAIL>"
                        />
                        {errors.email && <span className={styles.errorText}>{errors.email}</span>}
                    </div>
                </div>

                <div className={styles.field}>
                    <label htmlFor="subject" className={styles.label}>Subject</label>
                    <input
                        id="subject"
                        type="text"
                        value={formData.subject}
                        onChange={handleChange('subject')}
                        className={cx(styles.input, { [styles.error]: errors.subject })}
                        placeholder="What is this about?"
                    />
                    {errors.subject && <span className={styles.errorText}>{errors.subject}</span>}
                </div>

                <div className={styles.field}>
                    <label htmlFor="message" className={styles.label}>Message</label>
                    <textarea
                        id="message"
                        value={formData.message}
                        onChange={handleChange('message')}
                        className={cx(styles.textarea, { [styles.error]: errors.message })}
                        placeholder="Tell us more about your inquiry..."
                        rows={6}
                    />
                    {errors.message && <span className={styles.errorText}>{errors.message}</span>}
                </div>

                <button 
                    type="submit" 
                    className={styles.submitButton}
                    disabled={isLoading}
                >
                    {isLoading ? 'Sending...' : 'Send Message'}
                </button>
            </form>
        </div>
    );
};
