import styles from './card-4.module.scss';
import cx from 'classnames';

export interface Card4Props {
    className?: string;
}

/**
 * This component was created using Codux's Default new component template.
 * To create custom component templates, see https://help.codux.com/kb/en/article/kb16522
 */
export const Card4 = ({ className }: Card4Props) => {
    return <div className={cx(styles.root, className)}>Card4</div>;
};
