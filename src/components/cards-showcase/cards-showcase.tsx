import { CardBlog } from '../card-blog/card-blog';
import { CardProfile } from '../card-profile/card-profile';
import { CardProduct } from '../card-product/card-product';
import { CardNews } from '../card-news/card-news';
import { CardProfileCompact } from '../card-profile-compact/card-profile-compact';
import { CardTeamMember } from '../card-team-member/card-team-member';
import { CardUserStats } from '../card-user-stats/card-user-stats';
import { CardContact } from '../card-contact/card-contact';
import styles from './cards-showcase.module.scss';

export interface CardsShowcaseProps {
    className?: string;
}

/**
 * Showcase component displaying all card variants
 */
export const CardsShowcase = ({ className }: CardsShowcaseProps) => {
    return (
        <div className={styles.container}>
            <h1 className={styles.title}>Card Components Library</h1>
            
            <section className={styles.section}>
                <h2 className={styles.sectionTitle}>Content Cards</h2>
                <div className={styles.cardsGrid}>
                    <CardBlog />
                    <CardProduct />
                    <CardNews />
                </div>
            </section>

            <section className={styles.section}>
                <h2 className={styles.sectionTitle}>Profile Cards</h2>
                <div className={styles.cardsGrid}>
                    <CardProfile />
                    <CardProfileCompact 
                        name="Sarah Johnson"
                        title="UX Designer"
                        imageUrl="https://images.unsplash.com/photo-1494790108755-2616b612b1e9?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        onViewProfile={() => console.log('View profile clicked')}
                    />
                    <CardUserStats 
                        name="Alex Chen"
                        avatar="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        stats={{ posts: 324, followers: 12500, following: 890 }}
                        isVerified={true}
                        onFollow={() => console.log('Follow clicked')}
                    />
                </div>
            </section>

            <section className={styles.section}>
                <h2 className={styles.sectionTitle}>Business Cards</h2>
                <div className={styles.cardsGrid}>
                    <CardTeamMember 
                        name="Michael Rodriguez"
                        role="Senior Developer"
                        department="Engineering"
                        imageUrl="https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        email="<EMAIL>"
                        onContact={() => console.log('Contact clicked')}
                    />
                    <CardContact 
                        name="Emma Wilson"
                        title="Marketing Director"
                        company="TechCorp Inc."
                        avatar="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        email="<EMAIL>"
                        phone="+****************"
                        location="San Francisco, CA"
                        onMessage={() => console.log('Message clicked')}
                        onCall={() => console.log('Call clicked')}
                    />
                </div>
            </section>
        </div>
    );
};
