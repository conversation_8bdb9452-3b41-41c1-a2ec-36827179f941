.container {
    padding: 40px 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 50px;
    color: #2c2c54;
    font-family: Audiowide, system-ui;
    background: linear-gradient(90deg, #4946a7, #6366f1);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section {
    margin-bottom: 60px;
}

.sectionTitle {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #2c2c54;
    font-family: Audiowide, system-ui;
    text-align: center;
}

.cardsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    justify-items: center;
}
