import { ReactLogoAnimated } from '../react-logo-animated/react-logo-animated';
import styles from './welcome-screen.module.scss';
import cx from 'classnames';
import { useEffect, useState } from 'react';

export interface WelcomeScreenProps {
    className?: string;
    title?: string;
    subtitle?: string;
    onComplete?: () => void;
    autoHide?: boolean;
    duration?: number;
}

/**
 * Welcome screen with animated React logo for site entry
 */
export const WelcomeScreen = ({
    className,
    title = "Welcome to React",
    subtitle = "Building amazing experiences",
    onComplete,
    autoHide = true,
    duration = 4000
}: WelcomeScreenProps) => {
    const [isVisible, setIsVisible] = useState(true);
    const [showText, setShowText] = useState(false);
    const [isExiting, setIsExiting] = useState(false);

    useEffect(() => {
        // Show text after logo animation starts
        const textTimer = setTimeout(() => {
            setShowText(true);
        }, 1000);

        if (autoHide) {
            // Start exit animation
            const exitTimer = setTimeout(() => {
                setIsExiting(true);
            }, duration - 800);

            // Hide completely
            const hideTimer = setTimeout(() => {
                setIsVisible(false);
                if (onComplete) {
                    onComplete();
                }
            }, duration);

            return () => {
                clearTimeout(textTimer);
                clearTimeout(exitTimer);
                clearTimeout(hideTimer);
            };
        }

        return () => clearTimeout(textTimer);
    }, [autoHide, duration, onComplete]);

    if (!isVisible) return null;

    return (
        <div className={cx(
            styles.welcomeScreen,
            { [styles.exiting]: isExiting },
            className
        )}>
            <div className={styles.content}>
                <ReactLogoAnimated 
                    size="xl"
                    autoStart={true}
                />
                
                {showText && (
                    <div className={styles.textContent}>
                        <h1 className={styles.title}>{title}</h1>
                        <p className={styles.subtitle}>{subtitle}</p>
                        
                        <div className={styles.loadingBar}>
                            <div className={styles.loadingProgress}></div>
                        </div>
                    </div>
                )}
            </div>
            
            <div className={styles.particles}>
                {Array.from({ length: 20 }).map((_, i) => (
                    <div key={i} className={styles.particle} />
                ))}
            </div>
        </div>
    );
};
