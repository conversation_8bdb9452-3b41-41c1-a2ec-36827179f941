@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

@keyframes loadingProgress {
    from {
        width: 0%;
    }
    to {
        width: 100%;
    }
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.welcomeScreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: radial-gradient(circle at center, #1e1b4b 0%, #0f0f23 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeInUp 0.8s ease-out;
    
    &.exiting {
        animation: fadeOut 0.8s ease-in forwards;
    }
}

.content {
    text-align: center;
    position: relative;
    z-index: 2;
}

.textContent {
    margin-top: 40px;
    animation: fadeInUp 0.8s ease-out 0.5s both;
}

.title {
    font-size: 3rem;
    font-weight: 900;
    color: #ffffff;
    margin-bottom: 16px;
    font-family: Audiowide, system-ui;
    background: linear-gradient(90deg, #ffffff, #e2e8f0, #6366f1);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        background-position: 200% 0;
    }
    50% {
        background-position: -200% 0;
    }
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 32px;
    font-family: Monaco, monospace;
    font-weight: 300;
}

.loadingBar {
    width: 200px;
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    margin: 0 auto;
    overflow: hidden;
}

.loadingProgress {
    height: 100%;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #a855f7);
    border-radius: 2px;
    animation: loadingProgress 3s ease-out;
    box-shadow: none;
}

.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(99, 102, 241, 0.6);
    border-radius: 50%;
    animation: floatParticle 6s ease-in-out infinite;
    
    &:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
    &:nth-child(2) { top: 80%; left: 20%; animation-delay: 0.5s; }
    &:nth-child(3) { top: 40%; left: 80%; animation-delay: 1s; }
    &:nth-child(4) { top: 60%; left: 70%; animation-delay: 1.5s; }
    &:nth-child(5) { top: 30%; left: 30%; animation-delay: 2s; }
    &:nth-child(6) { top: 70%; left: 90%; animation-delay: 2.5s; }
    &:nth-child(7) { top: 10%; left: 60%; animation-delay: 3s; }
    &:nth-child(8) { top: 90%; left: 40%; animation-delay: 3.5s; }
    &:nth-child(9) { top: 50%; left: 15%; animation-delay: 4s; }
    &:nth-child(10) { top: 25%; left: 85%; animation-delay: 4.5s; }
    &:nth-child(11) { top: 75%; left: 55%; animation-delay: 5s; }
    &:nth-child(12) { top: 35%; left: 45%; animation-delay: 5.5s; }
    &:nth-child(13) { top: 65%; left: 25%; animation-delay: 6s; }
    &:nth-child(14) { top: 15%; left: 75%; animation-delay: 0.3s; }
    &:nth-child(15) { top: 85%; left: 65%; animation-delay: 0.8s; }
    &:nth-child(16) { top: 45%; left: 35%; animation-delay: 1.3s; }
    &:nth-child(17) { top: 55%; left: 95%; animation-delay: 1.8s; }
    &:nth-child(18) { top: 95%; left: 5%; animation-delay: 2.3s; }
    &:nth-child(19) { top: 5%; left: 95%; animation-delay: 2.8s; }
    &:nth-child(20) { top: 85%; left: 15%; animation-delay: 3.3s; }
}

@media (max-width: 768px) {
    .title {
        font-size: 2.2rem;
    }
    
    .subtitle {
        font-size: 1rem;
        padding: 0 20px;
    }
    
    .loadingBar {
        width: 150px;
    }
}
