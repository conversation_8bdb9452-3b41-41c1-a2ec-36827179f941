@import '../../styles/mixins.scss';

.div1 {
    @include card-base;
    margin: $spacing-5xl;
}
.button1 {
    @include button-primary;
    max-width: 50%;
    min-height: 25px;
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
}

.p1 {
    margin-bottom: $spacing-xl;
    font-family: $font-mono;
    font-size: $font-size-base;
    color: $text-primary;
    line-height: $line-height-relaxed;
}

.img1 {
    margin-bottom: $spacing-xl;
    border-radius: $radius-lg;
    width: 100%;
    height: auto;
    object-fit: cover;
}
.Headcard {
    background: linear-gradient(135deg, #2d5016 0%, #4a148c 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #2d5016;
    font-family: Audiowide, system-ui;
    font-size: 1.7rem;
    font-weight: 900;
    line-height: 1.3;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}
.Smallinfo {
    font-size: 12px;
    font-weight: 300;
    font-family: Monaco, monospace;
    color: #9a9a9a;
}
