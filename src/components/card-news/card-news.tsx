import '../../styles/utils.scss';
import styles from './card-news.module.scss';
import cx from 'classnames';

export interface CardNewsProps {
    className?: string;
}

/**
 * News card component with similar structure to CardBlog
 */
export const CardNews = ({ className }: CardNewsProps) => {
    return (
        <div className={cx(styles.card, styles.root)}>
            <div className={styles.div1} key={null}>
                <h1 className={styles.Headcard}>Breaking: New AI Technology Released</h1>
                <img
                    src="https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt="AI Technology"
                    className={styles.img1}
                />
                <div>
                    <h2 className={styles.Smallinfo}>Tech News • 2 hours ago</h2>
                </div>
                <p className={styles.p1}>
                    A revolutionary AI system has been unveiled today, promising to transform 
                    how we interact with technology. Industry experts are calling it groundbreaking.
                </p>
                <button className={styles.button1} onClick={undefined} type="button">
                    Read Article &gt;
                </button>
            </div>
        </div>
    );
};
