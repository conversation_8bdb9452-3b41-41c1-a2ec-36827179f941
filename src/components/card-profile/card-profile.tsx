import '../../styles/utils.scss';
import styles from './card-profile.module.scss';
import cx from 'classnames';

export interface CardProfileProps {
    className?: string;
}

/**
 * Profile card component with similar structure to CardBlog
 */
export const CardProfile = ({ className }: CardProfileProps) => {
    return (
        <div className={cx(styles.card, styles.root)}>
            <div className={styles.div1} key={null}>
                <h1 className={styles.Headcard}><PERSON></h1>
                <img
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt="Robin Doris"
                    className={styles.img1}
                />
                <div>
                    <h2 className={styles.Smallinfo}>Full Stack Developer</h2>
                </div>
                <p className={styles.p1}>
                    Passionate developer with 5+ years of experience in React, TypeScript, and Node.js. 
                    Always eager to learn new technologies and solve complex problems.
                </p>
                <button className={styles.button1} onClick={undefined} type="button">
                    View Profile &gt;
                </button>
            </div>
        </div>
    );
};
