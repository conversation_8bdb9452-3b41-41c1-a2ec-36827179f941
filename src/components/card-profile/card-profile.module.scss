@media (max-width: 900px) {
    .div1 {
        max-width: 98vw;
        min-width: 0;
        width: 100%;
        padding: 16px 6px;
        min-height: 220px;
        margin: 10px auto;
        border-radius: 12px;
    }
    .Headcard {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }
    .img1 {
        max-width: 90vw;
        height: auto;
        margin-bottom: 12px;
    }
    .button1 {
        min-width: 100px;
        font-size: 1rem;
        padding: 8px 12px;
        margin: 8px 0;
    }
    .p1 {
        font-size: 13px;
        margin-bottom: 10px;
    }
}
@import '../../styles/colors.scss';

.div1 {
    max-width: 400px;
    display: flex;
    flex-direction: column;
    padding: 30px 20px;
    flex-wrap: wrap;
    min-height: 500px;
    background-color: transparent;
    margin: 50px;
    border-radius: 25px;
    background-image: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
    background-position: 0% 0%;
    background-size: 105% 105%;
    background-repeat: repeat;
    background-attachment: scroll;
    background-origin: padding-box;
    background-clip: border-box;
    &:hover {
        outline: none;
        border: none;
    }
}
.button1 {
    max-width: 50%;
    border-radius: 25px;
    background: linear-gradient(135deg, #2d5016 0%, #4a148c 100%);
    color: #fff;
    font-weight: 600;
    min-height: 25px;
    padding: 5px;
    border: none;
    display: block;
    font-family: Monaco, monospace;
    box-shadow: none !important;
    &:hover {
        box-shadow: none !important;
        background: linear-gradient(135deg, #1b2f0d 0%, #6a1b9a 100%);
        transform: translateY(-2px) scale(1.04);
    }
}
.p1 {
    margin-bottom: 20px;
    font-family: Monaco, monospace;
    font-size: 16px;
    color: #a8a8a8;
}
.img1 {
    margin-bottom: 20px;
    border-radius: 15px;
}
.Headcard {
    background: linear-gradient(135deg, #2d5016 0%, #4a148c 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #2d5016;
    font-family: Audiowide, system-ui;
    font-size: 1.7rem;
    font-weight: 900;
    line-height: 1.3;
    margin-bottom: 20px;
    letter-spacing: -0.5px;
}
.Smallinfo {
    font-size: 12px;
    font-weight: 300;
    font-family: Monaco, monospace;
    color: #9a9a9a;
}
