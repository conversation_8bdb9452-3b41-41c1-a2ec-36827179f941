import { useState } from 'react';
import { WelcomeScreen } from '../welcome-screen/welcome-screen';
import { ComponentsShowcase } from '../components-showcase/components-showcase';
import styles from './app-with-welcome.module.scss';

export interface AppWithWelcomeProps {
    className?: string;
}

/**
 * Main app component with animated welcome screen
 */
export const AppWithWelcome = ({ className }: AppWithWelcomeProps) => {
    const [showWelcome, setShowWelcome] = useState(true);

    const handleWelcomeComplete = () => {
        setShowWelcome(false);
    };

    return (
        <div className={styles.app}>
            {showWelcome && (
                <WelcomeScreen 
                    title="Welcome to React"
                    subtitle="Building amazing experiences with modern components"
                    onComplete={handleWelcomeComplete}
                    autoHide={true}
                    duration={4000}
                />
            )}
            
            {!showWelcome && (
                <div className={styles.mainContent}>
                    <ComponentsShowcase />
                </div>
            )}
        </div>
    );
};
