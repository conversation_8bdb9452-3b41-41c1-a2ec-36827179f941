import '../../styles/utils.scss';
import styles from './card-team-member.module.scss';
import cx from 'classnames';

export interface CardTeamMemberProps {
    className?: string;
    name: string;
    role: string;
    department: string;
    imageUrl: string;
    email?: string;
    onContact?: () => void;
}

/**
 * Team member card component for company directories
 */
export const CardTeamMember = ({ 
    className, 
    name, 
    role, 
    department,
    imageUrl, 
    email,
    onContact 
}: CardTeamMemberProps) => {
    return (
        <div className={cx(styles.card, styles.root, className)}>
            <div className={styles.div1}>
                <div className={styles.imageContainer}>
                    <img
                        src={imageUrl}
                        alt={name}
                        className={styles.img1}
                    />
                    <div className={styles.statusBadge}></div>
                </div>
                <h2 className={styles.Headcard}>{name}</h2>
                <p className={styles.role}>{role}</p>
                <p className={styles.department}>{department}</p>
                {email && <p className={styles.email}>{email}</p>}
                <button 
                    className={styles.button1} 
                    onClick={onContact} 
                    type="button"
                >
                    Contact &gt;
                </button>
            </div>
        </div>
    );
};
