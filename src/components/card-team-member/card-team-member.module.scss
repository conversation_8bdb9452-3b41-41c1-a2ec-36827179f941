@import '../../styles/mixins.scss';

.div1 {
    @include card-base;
    max-width: 280px;
    align-items: center;
    text-align: center;
    margin: $spacing-xl;
    background: linear-gradient(180deg, #f093fb 0%, #f5576c 100%);
    min-height: 400px;
}

.imageContainer {
    position: relative;
    margin-bottom: $spacing-xl;
}

.img1 {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.statusBadge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: $success;
    border-radius: 50%;
    border: 3px solid $white;
}

.button1 {
    @include button-secondary;
    margin-top: auto;
    font-size: $font-size-sm;
}

.Headcard {
    font-weight: $font-weight-extrabold;
    line-height: $line-height-snug;
    margin-bottom: $spacing-sm;
    color: $white;
    font-family: $font-heading;
    font-size: $font-size-2xl;
}

.role {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    font-family: $font-mono;
    color: $white;
    margin: 0 0 $spacing-sm 0;
}

.department {
    font-size: $font-size-xs;
    font-weight: $font-weight-light;
    font-family: $font-mono;
    color: $text-muted;
    margin: 0 0 $spacing-md 0;
}

.email {
    font-size: 11px;
    font-weight: $font-weight-light;
    font-family: $font-mono;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 $spacing-xl 0;
    word-break: break-word;
}
