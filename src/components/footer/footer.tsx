import styles from './footer.module.scss';
import cx from 'classnames';
import PoweredByMyproxEuPng from '../../assets/powered by myprox.eu.png';

export interface FooterProps {
    className?: string;
}

/**
 * This component was created using Codux's Default new component template.
 * To create custom component templates, see https://help.codux.com/kb/en/article/kb16522
 */
export const Footer = ({ className }: FooterProps) => {
    return (
        <div className={cx(styles.root, styles.powerdby)}>
            <img src={PoweredByMyproxEuPng} className={styles.img1} />
        </div>
    );
};
