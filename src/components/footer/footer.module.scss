@import '../../styles/mixins.scss';

.root {
    background-color: transparent;
    padding: $spacing-xl 0;
}

.footer {
    display: flex;
    justify-content: flex-end;
    @include section-container;
}

.powerdby {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.img1 {
    margin: $spacing-5xl;
    transition: $transition-normal;

    &:hover {
        @include hover-lift(2px, 1.05);
    }
}
