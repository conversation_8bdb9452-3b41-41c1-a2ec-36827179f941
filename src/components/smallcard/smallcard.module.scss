@import '../../styles/mixins.scss';

.root {
    width: 350px;
    height: 200px;
    display: grid;
    grid-template-rows: repeat(3, 1fr);
    grid-template-columns: repeat(3, 1fr);
}

.small-card {
    width: 350px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(150deg, rgba(45, 80, 22, 0.8) 0%, rgba(74, 20, 140, 0.8) 100%);
    border-radius: $radius-md;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 200px;
    padding: $spacing-lg;
    transition: $transition-all;

    box-shadow: $shadow-card;
    transform: translateY(-10px);
    margin: $spacing-5xl;

    &:hover {
        @include hover-lift;
        background: linear-gradient(150deg, rgba(45, 80, 22, 0.9) 0%, rgba(74, 20, 140, 0.9) 100%);
        box-shadow: $shadow-card-hover;
    }
}
.img1 {
    display: block;
    flex-direction: row;
    height: 30px;
    width: 35px;
}
.div1 {
    align-content: center;
    display: flex;
    justify-content: space-around;
    align-items: center;
}
.div2 {
    display: flex;
    flex-direction: column;
    align-items: start;
    width: 200px;
}
.header1 {
    color: $text-light;
    font-family: $font-heading;
    font-weight: $font-weight-bold;
    margin-bottom: $spacing-sm;
}

.p1 {
    color: $text-secondary;
    font-family: $font-mono;
    font-size: $font-size-sm;
    line-height: $line-height-relaxed;
}
