import '../../styles/global.scss';
import styles from './smallcard.module.scss';
import cx from 'classnames';
import Group81Png from '../../assets/group 81.png';

export interface SmallcardProps {
    className?: string;
}

/**
 * This component was created using Codux's Default new component template.
 * To create custom component templates, see https://help.codux.com/kb/en/article/kb16522
 */
export const Smallcard = ({ className }: SmallcardProps) => {
    return (
        <div className={cx(styles['small-card'], className)}>
            <div className={styles.div1}>
                <img src={Group81Png} className={styles.img1} />
                <div className={styles.div2}>
                    <h1 className="text-3xl">Heading 1</h1>
                    <p className={styles.p1}>
                        This is a placeholder paragraph. It is here to provide structure while y...
                    </p>
                </div>
            </div>
        </div>
    );
};
