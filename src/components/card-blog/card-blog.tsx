import '../../styles/typography.scss';
import '../../styles/global.scss';
import '../../styles/utils.scss';
import styles from './card-blog.module.scss';
import cx from 'classnames';

export interface CardBlogProps {
    className?: string;
}

/**
 * This component was created using Codux's Default new component template.
 * To create custom component templates, see https://help.codux.com/kb/en/article/kb16522
 */
export const CardBlog = ({ className }: CardBlogProps) => {
    return (
        <div className={cx(styles.root, styles.card)}>
            <div className={styles.div1} key={null}>
                <h1 className={cx(styles.Headcard, 'text-3xl')}>
                    Ako som spravil svoj prvý react component
                </h1>
                <img
                    src="https://images.unsplash.com/photo-1669023414162-5bb06bbff0ec?q=80&w=1632&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                    alt=""
                    className={styles.img1}
                />
                <div>
                    <h2 className={styles.Smallinfo}>Small Font</h2>
                </div>
                <p className={styles.p1}>
                    This is a placeholder paragraph. It is here to provide structure while you work
                    on your content. You can replace this text with your own 
                </p>
                <button className={styles.button1} onClick={undefined} type="button">
                    Read more &gt;
                </button>
            </div>
        </div>
    );
};
