@media (max-width: 900px) {
    .div1 {
        max-width: 98vw;
        min-width: 0;
        width: 100%;
        padding: 16px 6px;
        min-height: 220px;
        margin: 10px auto;
        border-radius: 12px;
    }
    .Headcard {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }
    .img1 {
        max-width: 90vw;
        height: auto;
        margin-bottom: 12px;
    }
    .button1 {
        min-width: 100px;
        font-size: 1rem;
        padding: 8px 12px;
        margin: 8px 0;
    }
    .p1 {
        font-size: 13px;
        margin-bottom: 10px;
    }
}
@import '../../styles/mixins.scss';

.div1 {
    @include card-base;
    width: 350px;
    margin: 0;
}

.button1 {
    @include button-primary;
    max-width: 50%;
    min-height: 25px;
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
}

.p1 {
    margin-bottom: $spacing-xl;
    font-family: $font-mono;
    font-size: $font-size-sm;
    color: $text-primary;
    line-height: $line-height-relaxed;
}

.img1 {
    margin-bottom: $spacing-xl;
    border-radius: $radius-lg;
    width: 100%;
    height: auto;
    object-fit: cover;
}

.Smallinfo {
    font-size: $font-size-xs;
    font-weight: $font-weight-light;
    font-family: $font-mono;
    color: $text-secondary;
    margin-top: auto;
}
